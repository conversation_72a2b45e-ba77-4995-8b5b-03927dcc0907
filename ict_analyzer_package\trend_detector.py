# ict_analyzer_package/trend_detector.py
"""
تشخیص روند بازار با استفاده از EMA، ADX و تحلیل حجم (نسخه بهبود یافته)
Market trend detection using EMAs, ADX, and volume analysis (Enhanced Version).
"""
import pandas as pd
import logging
import pandas_ta as ta
import numpy as np 

from . import constants 
from .helpers import ensure_atr

logger = logging.getLogger(__name__)

# --- Default values for new parameters if not provided via cfg from analyzer ---
# These are also defined in constants.py and should be sourced from there by analyzer.py
DEFAULT_SLOPE_CALCULATION_PERIODS_TD = constants.DEFAULT_SLOPE_CALCULATION_PERIODS
ATR_SLOPE_THRESHOLD_FLAT_TD = constants.ATR_SLOPE_THRESHOLD_FLAT
ATR_SLOPE_THRESHOLD_GENTLE_TD = constants.ATR_SLOPE_THRESHOLD_GENTLE
MIN_PRACTICAL_ATR_PERCENT_OF_PRICE_TD = constants.MIN_PRACTICAL_ATR_PERCENT_OF_PRICE
ATR_MIN_VALUE_PRACTICAL_SLOPE_TD = constants.ATR_MIN_VALUE_PRACTICAL_SLOPE

VOLUME_RATIO_VERY_LOW_TD = constants.VOLUME_RATIO_THRESHOLD_VERY_LOW
VOLUME_RATIO_LOW_TD = constants.VOLUME_RATIO_THRESHOLD_LOW
VOLUME_RATIO_HIGH_TD = constants.VOLUME_RATIO_THRESHOLD_HIGH
VOLUME_RATIO_VERY_HIGH_TD = constants.VOLUME_RATIO_THRESHOLD_VERY_HIGH
# --- End of Default Values ---

def _determine_ema_slope_atr_normalized(
    ema_series: pd.Series,
    price_series_for_atr_fallback: pd.Series,
    atr_series: pd.Series,
    slope_periods: int,
    flat_thresh_atr: float,
    gentle_thresh_atr: float,
    min_practical_atr_pct_price: float,
    min_absolute_practical_atr: float
) -> str: # # Helper from response #37, adapted for trend_detector
    """
    Determines EMA slope based on ATR normalization.
    Returns categories like "steep_up", "gentle_up", "flat", "gentle_down", "steep_down",
    or "flat_atr_unreliable" if ATR is too small for meaningful slope calculation.
    """
    if not isinstance(ema_series, pd.Series) or ema_series.empty or \
       not isinstance(price_series_for_atr_fallback, pd.Series) or price_series_for_atr_fallback.empty or \
       not isinstance(atr_series, pd.Series) or atr_series.empty:
        return "flat_data_insufficient"

    if len(ema_series) < slope_periods + 1 or len(atr_series) < 1 or len(price_series_for_atr_fallback) < 1:
        return "flat_data_insufficient"

    current_ema = ema_series.iloc[-1]
    past_ema = ema_series.iloc[-1 - slope_periods]
    current_atr = atr_series.iloc[-1]
    current_price_for_atr_fallback = price_series_for_atr_fallback.iloc[-1]

    if pd.isna(current_ema) or pd.isna(past_ema) or pd.isna(current_price_for_atr_fallback):
        return "flat_data_insufficient"

    price_based_min_atr = current_price_for_atr_fallback * min_practical_atr_pct_price
    practical_min_atr_for_slope = max(min_absolute_practical_atr, price_based_min_atr)

    if pd.isna(current_atr) or current_atr < practical_min_atr_for_slope :
        logger.debug(f"ATR ({current_atr}) is below practical minimum ({practical_min_atr_for_slope}) for slope calculation in trend detector. EMA: {ema_series.name if hasattr(ema_series, 'name') else 'Unknown'}")
        return "flat_atr_unreliable"

    raw_slope_change = current_ema - past_ema
    slope_atr_normalized = raw_slope_change / current_atr 

    if slope_atr_normalized > gentle_thresh_atr: return "steep_up"
    elif slope_atr_normalized > flat_thresh_atr: return "gentle_up"
    elif slope_atr_normalized < -gentle_thresh_atr: return "steep_down"
    elif slope_atr_normalized < -flat_thresh_atr: return "gentle_down"
    else: return "flat"

def determine_market_trend( #
    df: pd.DataFrame,
    short_ema_p: int = constants.SHORT_EMA_PERIOD, 
    mid_ema_p: int = constants.MID_EMA_PERIOD, 
    long_ema_p: int = constants.LONG_EMA_PERIOD, 
    adx_p: int = constants.ADX_PERIOD, 
    adx_strong_thresh: float = constants.ADX_THRESHOLD_STRONG_TREND, 
    adx_weak_thresh: float = constants.ADX_THRESHOLD_WEAK_TREND_OR_RANGE, 
    ema_compression_thresh_pct: float = constants.EMA_COMPRESSION_THRESHOLD_PERCENT, 
    slope_calc_periods: int = DEFAULT_SLOPE_CALCULATION_PERIODS_TD,
    atr_slope_flat_thresh: float = ATR_SLOPE_THRESHOLD_FLAT_TD,
    atr_slope_gentle_thresh: float = ATR_SLOPE_THRESHOLD_GENTLE_TD,
    min_practical_atr_pct_price_for_slope: float = MIN_PRACTICAL_ATR_PERCENT_OF_PRICE_TD,
    min_abs_practical_atr_for_slope: float = ATR_MIN_VALUE_PRACTICAL_SLOPE_TD,
    vol_ratio_very_low: float = VOLUME_RATIO_VERY_LOW_TD,
    vol_ratio_low: float = VOLUME_RATIO_LOW_TD,
    vol_ratio_high: float = VOLUME_RATIO_HIGH_TD,
    vol_ratio_very_high: float = VOLUME_RATIO_VERY_HIGH_TD
) -> dict:
    trend_details = {
        "primary_trend_ema_rating": "neutral", "primary_trend_adx_direction": "ranging",
        "overall_trend_consensus": "ranging", "trend_strength_adx_category": "N/A",
        "adx_value": None, "plus_di_value": None, "minus_di_value": None,
        "ema_short_value": None, "ema_mid_value": None, "ema_long_value": None,
        "price_position_vs_emas": "neutral",
        "ema_slope_short": "flat_data_insufficient", "ema_slope_mid": "flat_data_insufficient", "ema_slope_long": "flat_data_insufficient",
        "volume_analysis": {"intensity": "N/A", "ratio_curr_avg": None, "current_volume": None, "average_volume": None, "notes": "Analysis not run"},
        "ema_compression_status": "not_compressed", "market_condition_notes": [],
        "trend_profile": {"primary_direction": "ranging", "ema_based_strength": "neutral", "adx_based_strength": "N/A", "adx_direction_agreement": "N/A", "volume_support": "N/A", "slope_short": "N/A", "slope_mid": "N/A", "slope_long": "N/A"}
    }
    min_required_data = max(long_ema_p, adx_p, slope_calc_periods) + 5
    if df.empty or len(df) < min_required_data:
        trend_details["market_condition_notes"].append("Data insufficient for full trend analysis.")
        if not df.empty and 'close' in df.columns and len(df['close']) > 0 and pd.notna(df['close'].iloc[-1]): trend_details["price_position_vs_emas"] = f"Price: {df['close'].iloc[-1]}"
        else: trend_details["price_position_vs_emas"] = "Price: N/A"
        return trend_details

    df_copy = df.copy()
    required_cols = ['open', 'high', 'low', 'close']
    if not all(col in df_copy.columns for col in required_cols):
        df_copy = df_copy.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, errors='ignore')
        if not all(col in df_copy.columns for col in required_cols): trend_details["market_condition_notes"].append("Critical OHLC columns missing."); return trend_details

    df_copy = ensure_atr(df_copy, period=constants.ATR_PERIOD_DEFAULT, col_name='ATR_Value')
    if 'ATR_Value' not in df_copy.columns or df_copy['ATR_Value'].isnull().all():
        trend_details["market_condition_notes"].append("ATR calculation failed; trend quality reduced.")
        df_copy['ATR_Value'] = (df_copy['close'] * constants.ATR_ABNORMAL_PRICE_PERCENTAGE_FALLBACK).fillna(constants.ATR_MIN_VALUE * 10)

    try:
        ema_s_col, ema_m_col, ema_l_col = f'EMA_{short_ema_p}', f'EMA_{mid_ema_p}', f'EMA_{long_ema_p}'
        df_copy[ema_s_col] = ta.ema(df_copy["close"], length=short_ema_p); df_copy[ema_m_col] = ta.ema(df_copy["close"], length=mid_ema_p); df_copy[ema_l_col] = ta.ema(df_copy["close"], length=long_ema_p)
        trend_details.update({"ema_short_value": round(df_copy[ema_s_col].iloc[-1],5) if pd.notna(df_copy[ema_s_col].iloc[-1]) else None, "ema_mid_value": round(df_copy[ema_m_col].iloc[-1],5) if pd.notna(df_copy[ema_m_col].iloc[-1]) else None, "ema_long_value": round(df_copy[ema_l_col].iloc[-1],5) if pd.notna(df_copy[ema_l_col].iloc[-1]) else None})
        trend_details["ema_slope_short"] = _determine_ema_slope_atr_normalized(df_copy[ema_s_col], df_copy["close"], df_copy['ATR_Value'], slope_calc_periods, atr_slope_flat_thresh, atr_slope_gentle_thresh, min_practical_atr_pct_price_for_slope, min_abs_practical_atr_for_slope)
        trend_details["ema_slope_mid"] = _determine_ema_slope_atr_normalized(df_copy[ema_m_col], df_copy["close"], df_copy['ATR_Value'], slope_calc_periods, atr_slope_flat_thresh, atr_slope_gentle_thresh, min_practical_atr_pct_price_for_slope, min_abs_practical_atr_for_slope)
        trend_details["ema_slope_long"] = _determine_ema_slope_atr_normalized(df_copy[ema_l_col], df_copy["close"], df_copy['ATR_Value'], slope_calc_periods, atr_slope_flat_thresh, atr_slope_gentle_thresh, min_practical_atr_pct_price_for_slope, min_abs_practical_atr_for_slope)
        trend_details["trend_profile"]["slope_short"]=trend_details["ema_slope_short"]; trend_details["trend_profile"]["slope_mid"]=trend_details["ema_slope_mid"]; trend_details["trend_profile"]["slope_long"]=trend_details["ema_slope_long"]
    except Exception as e_ema: trend_details["market_condition_notes"].append(f"EMA calc error: {e_ema}"); trend_details["ema_slope_short"]=trend_details["ema_slope_mid"]=trend_details["ema_slope_long"]="flat_error"; trend_details["trend_profile"]["slope_short"]=trend_details["trend_profile"]["slope_mid"]=trend_details["trend_profile"]["slope_long"]="flat_error"

    try:
        adx_df = ta.adx(df_copy["high"], df_copy["low"], df_copy["close"], length=adx_p)
        if adx_df is not None and not adx_df.empty and f'ADX_{adx_p}' in adx_df.columns:
            adx_val, plus_di_val, minus_di_val = adx_df[f'ADX_{adx_p}'].iloc[-1], adx_df[f'DMP_{adx_p}'].iloc[-1], adx_df[f'DMN_{adx_p}'].iloc[-1]
            trend_details.update({"adx_value": round(adx_val,2) if pd.notna(adx_val) else None, "plus_di_value": round(plus_di_val,2) if pd.notna(plus_di_val) else None, "minus_di_value": round(minus_di_val,2) if pd.notna(minus_di_val) else None})
            if pd.notna(adx_val):
                if adx_val > adx_strong_thresh: trend_details["trend_strength_adx_category"] = "strong"
                elif adx_val < adx_weak_thresh: trend_details["trend_strength_adx_category"] = "weak"
                else: trend_details["trend_strength_adx_category"] = "moderate"
                trend_details["trend_profile"]["adx_based_strength"]=trend_details["trend_strength_adx_category"]
                if pd.notna(plus_di_val) and pd.notna(minus_di_val):
                    if trend_details["trend_strength_adx_category"] != "weak":
                        if plus_di_val > minus_di_val: trend_details["primary_trend_adx_direction"] = "bullish"
                        elif minus_di_val > plus_di_val: trend_details["primary_trend_adx_direction"] = "bearish"
                        else: trend_details["primary_trend_adx_direction"] = "ranging"
                    else: trend_details["primary_trend_adx_direction"] = "ranging"
            else: trend_details["trend_strength_adx_category"]="N/A"; trend_details["primary_trend_adx_direction"]="N/A"; trend_details["trend_profile"]["adx_based_strength"]="N/A"
        else: logger.warning(f"ADX calc failed for period {adx_p}."); trend_details["trend_strength_adx_category"]="N/A"; trend_details["primary_trend_adx_direction"]="N/A"; trend_details["trend_profile"]["adx_based_strength"]="N/A"; trend_details["market_condition_notes"].append("ADX calc failed.")
    except Exception as e_adx: trend_details["market_condition_notes"].append(f"ADX calc error: {e_adx}"); trend_details["trend_strength_adx_category"]="N/A"; trend_details["primary_trend_adx_direction"]="N/A"; trend_details["trend_profile"]["adx_based_strength"]="N/A"

    if 'volume' in df_copy.columns and df_copy['volume'].notna().any(): #
        # ... (Volume analysis logic from response #13) ...
        avg_vol_lookback = mid_ema_p; 
        if len(df_copy) > avg_vol_lookback:
            valid_volumes = df_copy['volume'].dropna()
            if len(valid_volumes) >= avg_vol_lookback : avg_volume = valid_volumes.rolling(window=avg_vol_lookback).mean().iloc[-1]
            else: avg_volume = pd.NA 
            current_volume = df_copy['volume'].iloc[-1]; ratio, intensity = None, "N/A"
            if pd.notna(avg_volume) and avg_volume > 0 and pd.notna(current_volume):
                ratio = round(current_volume / avg_volume, 2)
                if ratio >= vol_ratio_very_high: intensity = "very_high"
                elif ratio >= vol_ratio_high: intensity = "high"
                elif ratio <= vol_ratio_very_low: intensity = "very_low"
                elif ratio <= vol_ratio_low: intensity = "low"
                else: intensity = "average"
            elif pd.isna(current_volume): intensity = "N/A_current_vol_na"
            else: intensity = "N/A_avg_vol_calc_issue"
            trend_details["volume_analysis"] = {"intensity":intensity, "ratio_curr_avg":ratio, "current_volume":round(current_volume,2) if pd.notna(current_volume) else None, "average_volume":round(avg_volume,2) if pd.notna(avg_volume) else None, "notes":f"Volume intensity: {intensity}" + (f" (Ratio: {ratio})" if ratio is not None else "")}
            trend_details["trend_profile"]["volume_support"]=intensity
        else: trend_details["volume_analysis"]["notes"] = f"Not enough data for {avg_vol_lookback}-period avg vol."
    else: trend_details["volume_analysis"]["notes"] = "No volume data available."

    last_close = df_copy['close'].iloc[-1] if pd.notna(df_copy['close'].iloc[-1]) else None #
    # ... (Price Position vs EMAs & EMA Alignment/Rating logic from response #13) ...
    ema_s, ema_m, ema_l = trend_details["ema_short_value"], trend_details["ema_mid_value"], trend_details["ema_long_value"]
    ema_rating = "ranging_or_conflicted_emas" 
    if last_close and isinstance(ema_s,(int,float)) and isinstance(ema_m,(int,float)) and isinstance(ema_l,(int,float)):
        pos_vs_emas_cats = {(last_close > ema_s > ema_m > ema_l): "perfect_bullish_order_above_all", (last_close < ema_s < ema_m < ema_l): "perfect_bearish_order_below_all", (last_close > ema_s and last_close > ema_m and last_close > ema_l): "above_all_emas", (last_close < ema_s and last_close < ema_m and last_close < ema_l): "below_all_emas", (ema_m < last_close < ema_s): "between_short_mid_emas_short_higher", (ema_s < last_close < ema_m): "between_short_mid_emas_mid_higher"}
        trend_details["price_position_vs_emas"] = next((v for k,v in pos_vs_emas_cats.items() if k), "mixed_emas_position")
        s_slope, m_slope, l_slope = trend_details["ema_slope_short"], trend_details["ema_slope_mid"], trend_details["ema_slope_long"]
        is_s_bull,is_m_bull,is_l_bull = "up" in s_slope, "up" in m_slope, "up" in l_slope
        is_s_bear,is_m_bear,is_l_bear = "down" in s_slope, "down" in m_slope, "down" in l_slope
        if ema_s > ema_m > ema_l and is_s_bull and is_m_bull : ema_rating = "strong_bullish" if is_l_bull else "bullish_htf_slope_flat_or_contra";_ = "steep_up" in s_slope and "steep_up" in m_slope and (ema_rating:="very_strong_bullish_momentum")
        elif ema_s < ema_m < ema_l and is_s_bear and is_m_bear: ema_rating = "strong_bearish" if is_l_bear else "bearish_htf_slope_flat_or_contra";_ = "steep_down" in s_slope and "steep_down" in m_slope and (ema_rating:="very_strong_bearish_momentum")
        elif ema_s > ema_m and is_s_bull: ema_rating = "bullish_developing"
        elif ema_s < ema_m and is_s_bear: ema_rating = "bearish_developing"
        elif any("unreliable" in s for s in [s_slope, m_slope, l_slope]): ema_rating = "ranging_unreliable_slopes"
        trend_details["primary_trend_ema_rating"] = ema_rating
        trend_details["trend_profile"]["ema_based_strength"] = ema_rating.split('_')[0] if '_' in ema_rating else ema_rating
        avg_price_comp = (ema_s+ema_m+ema_l)/3 if (ema_s+ema_m+ema_l)!=0 else (last_close if last_close and last_close > 0 else 1)
        s_m_diff_pct = abs(ema_s-ema_m)/avg_price_comp if avg_price_comp!=0 else float('inf'); m_l_diff_pct = abs(ema_m-ema_l)/avg_price_comp if avg_price_comp!=0 else float('inf')
        if s_m_diff_pct < ema_compression_thresh_pct and m_l_diff_pct < ema_compression_thresh_pct: trend_details["ema_compression_status"]="all_emas_compressed"; trend_details["market_condition_notes"].append("EMAs highly compressed.")
        elif s_m_diff_pct < ema_compression_thresh_pct: trend_details["ema_compression_status"]="short_mid_compressed"
    else: trend_details["primary_trend_ema_rating"]="N/A_emas_missing"; trend_details["trend_profile"]["ema_based_strength"]="N/A"

    # Overall Trend Consensus (Refined Logic from response #13)
    # ... (Full consensus logic from response #13) ...
    ema_rating = trend_details["primary_trend_ema_rating"]; adx_dir = trend_details["primary_trend_adx_direction"]; adx_strength = trend_details["trend_strength_adx_category"]; vol_intensity = trend_details["volume_analysis"]["intensity"]; consensus = "uncertain_market_conditions"; trend_profile = trend_details["trend_profile"]
    if "bullish" in ema_rating:
        trend_profile["primary_direction"]="bullish"
        if adx_dir=="bullish": trend_profile["adx_direction_agreement"]="confirms_ema";_ = adx_strength=="strong" and (consensus:="very_strong_bullish"+("_vol_confirmed_high" if vol_intensity in ["high","very_high"] else "")) or (adx_strength=="moderate" and (consensus:="strong_bullish_developing_momentum")) or (consensus:="bullish_bias_weak_adx_support")
        elif adx_dir=="ranging" or adx_strength=="weak" or adx_strength=="N/A": trend_profile["adx_direction_agreement"]="neutral_adx_or_weak"; consensus="bullish_bias_adx_non_supportive"
        elif adx_dir=="bearish": trend_profile["adx_direction_agreement"]="conflicts_ema_bearish_adx"; consensus="strong_bullish_emas_vs_bearish_adx_pullback_or_divergence" if "strong_bullish" in ema_rating or "very_strong_bullish" in ema_rating else "bullish_emas_vs_bearish_adx_caution"
        else: trend_profile["adx_direction_agreement"]="N/A"; consensus=ema_rating 
    elif "bearish" in ema_rating:
        trend_profile["primary_direction"]="bearish"
        if adx_dir=="bearish": trend_profile["adx_direction_agreement"]="confirms_ema";_ = adx_strength=="strong" and (consensus:="very_strong_bearish"+("_vol_confirmed_high" if vol_intensity in ["high","very_high"] else "")) or (adx_strength=="moderate" and (consensus:="strong_bearish_developing_momentum")) or (consensus:="bearish_bias_weak_adx_support")
        elif adx_dir=="ranging" or adx_strength=="weak" or adx_strength=="N/A": trend_profile["adx_direction_agreement"]="neutral_adx_or_weak"; consensus="bearish_bias_adx_non_supportive"
        elif adx_dir=="bullish": trend_profile["adx_direction_agreement"]="conflicts_ema_bullish_adx"; consensus="strong_bearish_emas_vs_bullish_adx_pullback_or_divergence" if "strong_bearish" in ema_rating or "very_strong_bearish" in ema_rating else "bearish_emas_vs_bullish_adx_caution"
        else: trend_profile["adx_direction_agreement"]="N/A"; consensus=ema_rating
    elif "ranging" in ema_rating or "unreliable" in ema_rating or trend_details["ema_compression_status"]!="not_compressed":
        trend_profile["primary_direction"]="ranging"; trend_profile["adx_direction_agreement"]="N/A" if adx_dir=="ranging" else adx_dir
        if adx_strength=="weak" or adx_dir=="ranging" or adx_strength=="N/A": consensus="ranging_market_confirmed_low_momentum"
        elif adx_strength=="moderate": consensus=f"ranging_emas_with_{adx_dir}_adx_pressure"
        elif adx_strength=="strong": consensus=f"ranging_emas_with_strong_{adx_dir}_adx_breakout_potential"
        else: consensus="ranging_market_complex"
    trend_details["overall_trend_consensus"]=consensus; trend_details["trend_profile"]=trend_profile
    logger.info(f"Final Trend: {trend_details['overall_trend_consensus']}, EMA: {trend_details['primary_trend_ema_rating']}, ADX Str: {trend_details['trend_strength_adx_category']}")
    return trend_details

logger.info("ict_analyzer_package/trend_detector.py (final enhanced version) loaded.")
# ict_analyzer_package/ote_detector.py
"""
شناسایی نواحی ورود بهینه (Optimal Trade Entry - OTE) (نسخه بهبود یافته)
Detects Optimal Trade Entry (OTE) zones with inducement sweep checks and refined confluence scoring.
"""
import pandas as pd
import logging
from typing import Tuple, List, Dict, Optional, Any # CORRECTED IMPORT

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def _check_inducement_sweep_before_ote( 
    dealing_range: dict,
    ote_zone_min_abs: float, 
    ote_zone_max_abs: float, 
    liquidity_pools: list,
    current_analysis_timestamp: pd.Timestamp, 
    cfg: dict,
    current_atr: float
) -> Tuple[bool, Optional[Dict]]: #
    swept_inducement_details = None; inducement_swept_flag = False
    dr_high_price = float(dealing_range['high_price']); dr_low_price = float(dealing_range['low_price'])
    for liq_pool in liquidity_pools:
        if not isinstance(liq_pool, dict): continue
        is_confirmed_inducement = "Confirmed_Inducement" in liq_pool.get('type', '')
        was_swept = liq_pool.get('current_status') == 'swept'
        if not (is_confirmed_inducement and was_swept): continue
        liq_price = liq_pool.get('price_level'); sweep_event_ts_str = liq_pool.get('timestamp_event')
        if liq_price is None or sweep_event_ts_str is None: continue
        sweep_event_ts = pd.to_datetime(sweep_event_ts_str, utc=True)
        dr_leg_start_ts = pd.to_datetime(dealing_range.get('low_timestamp' if dealing_range['type'] == 'bullish' else 'high_timestamp'), utc=True)
        if not (dr_leg_start_ts <= sweep_event_ts <= current_analysis_timestamp): continue
        if dealing_range['type'] == 'bullish':
            if liq_pool.get('type','').endswith('SL') and ote_zone_max_abs < liq_price < dr_high_price:
                inducement_swept_flag = True; swept_inducement_details = liq_pool.copy(); break
        elif dealing_range['type'] == 'bearish':
            if liq_pool.get('type','').endswith('SH') and dr_low_price < liq_price < ote_zone_min_abs:
                inducement_swept_flag = True; swept_inducement_details = liq_pool.copy(); break
    return inducement_swept_flag, swept_inducement_details

def find_ote_levels(
    df: pd.DataFrame,
    bos_choch_list: list, 
    pd_arrays: dict,      
    liquidity_pools: list, 
    killzones_info: Optional[List[Dict]] = None, 
    atr_period: int = constants.ATR_PERIOD_DEFAULT, 
    cfg: Optional[Dict[str, Any]] = None # cfg uses 'Any' now correctly imported
) -> list: #
    ote_areas_details = []
    if df.empty or not bos_choch_list: 
        logger.debug("Dataframe or BOS/CHoCH list is empty for OTE detection.")
        return ote_areas_details

    config = cfg if cfg is not None else {} 

    df_with_atr = ensure_atr(df.copy(), period=atr_period) 
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all():
        logger.error("ATR_Value column missing/invalid in find_ote_levels.")
        df_with_atr['ATR_Value'] = constants.ATR_MIN_VALUE * 10

    current_analysis_timestamp = pd.to_datetime(df_with_atr.index[-1], utc=True)

    current_atr_val = df_with_atr['ATR_Value'].iloc[-1] 
    if not pd.notna(current_atr_val) or current_atr_val <= constants.ATR_MIN_VALUE: 
         current_atr_val = max(constants.ATR_MIN_VALUE *10, (df_with_atr[('High' if 'High' in df_with_atr.columns else 'high')].iloc[-1] - df_with_atr[('Low' if 'Low' in df_with_atr.columns else 'low')].iloc[-1]) * 0.1 if (df_with_atr[('High' if 'High' in df_with_atr.columns else 'high')].iloc[-1] - df_with_atr[('Low' if 'Low' in df_with_atr.columns else 'low')].iloc[-1]) > 0 else constants.ATR_MIN_VALUE * 10)

    valid_structure_events_for_ote = [
        e for e in bos_choch_list if isinstance(e,dict) and
        e.get('new_dealing_range_established', False) and
        isinstance(e.get('new_dealing_range_details'), dict) and
        all(k in e['new_dealing_range_details'] for k in ['low_price', 'high_price', 'type']) and
        e.get('confirmation_quality_score', 0) >= config.get("ote_min_bos_choch_quality", constants.OTE_MIN_BOS_CHOCH_QUALITY_DEFAULT)
    ] 
    if not valid_structure_events_for_ote: 
        logger.debug("No valid structure events with new dealing ranges for OTE detection.")
        return ote_areas_details

    fvgs = pd_arrays.get('fvgs', []); order_blocks = pd_arrays.get('order_blocks', []); breaker_blocks = pd_arrays.get('breaker_blocks', [])

    for event_data in valid_structure_events_for_ote: 
        dealing_range = event_data['new_dealing_range_details'] 
        ote_trade_direction = dealing_range['type'] 
        
        dr_start_price, dr_end_price = (float(dealing_range['low_price']), float(dealing_range['high_price'])) if ote_trade_direction == 'bullish' else (float(dealing_range['high_price']), float(dealing_range['low_price']))
        
        price_range_abs = abs(dr_end_price - dr_start_price) 
        if price_range_abs < current_atr_val * config.get("ote_min_dealing_range_atr_mult", constants.OTE_MIN_DEALING_RANGE_ATR_MULT_DEFAULT) : continue 

        fib_levels_config = config.get("ote_fib_levels", constants.OTE_FIB_LEVELS_STANDARD_DEFAULT) 
        fib_levels_calculated = {} 
        ote_prices_in_zone = []

        for level in fib_levels_config: 
            price = round(dr_start_price + (level * price_range_abs) if ote_trade_direction == 'bearish' else dr_end_price - (level * price_range_abs), 5)
            fib_levels_calculated[f'fib_{level*1000:.0f}'] = price 
            ote_prices_in_zone.append(price)
        
        actual_ote_zone_low = min(ote_prices_in_zone)
        actual_ote_zone_high = max(ote_prices_in_zone)

        confluent_pd_arrays_list = [] 
        ote_score = event_data.get('confirmation_quality_score', 0) * config.get("ote_bos_choch_quality_factor", constants.OTE_BOS_CHOCH_QUALITY_FACTOR_DEFAULT) 

        pda_configurations = [
            (fvgs, "FVG", 'type', 'importance_score', config.get("ote_pd_array_confluence_base_bonus", constants.OTE_PD_ARRAY_CONFLUENCE_BASE_BONUS) + 0.5),
            (order_blocks, "Order_Block", 'type', 'strength_score', config.get("ote_pd_array_confluence_base_bonus", constants.OTE_PD_ARRAY_CONFLUENCE_BASE_BONUS) + 1.0),
            (breaker_blocks, "Breaker_Block", 'type', 'strength_score', config.get("ote_pd_array_confluence_base_bonus", constants.OTE_PD_ARRAY_CONFLUENCE_BASE_BONUS) + 1.5)
        ]
        for pda_list, pda_name, pda_type_key, pda_strength_key, pda_base_bonus in pda_configurations: 
            for pda_item in [item for item in pda_list if isinstance(item, dict) and item.get(pda_strength_key,0) > config.get("ote_min_pda_strength_for_confluence", constants.OTE_MIN_PDA_STRENGTH_FOR_CONFLUENCE_DEFAULT)]: 
                pda_item_type = pda_item.get(pda_type_key, '').lower() 
                pda_matches_dir = (ote_trade_direction == 'bullish' and 'bullish' in pda_item_type) or \
                                  (ote_trade_direction == 'bearish' and 'bearish' in pda_item_type) 
                if not pda_matches_dir: continue

                pda_top = pda_item.get('top_price', pda_item.get('end_price')) 
                pda_bottom = pda_item.get('bottom_price', pda_item.get('start_price')) 
                if pda_top is None or pda_bottom is None: continue 
                pda_min_price, pda_max_price = min(pda_top, pda_bottom), max(pda_top, pda_bottom) 

                if max(actual_ote_zone_low, pda_min_price) < min(actual_ote_zone_high, pda_max_price): 
                    confluent_pd_arrays_list.append({'type': pda_name, 'details': pda_item.copy()}) 
                    ote_score += pda_base_bonus + (pda_item.get(pda_strength_key, 0) * config.get("ote_pd_array_strength_factor", constants.OTE_PD_ARRAY_STRENGTH_FACTOR_DEFAULT)) 
                    
                    pda_sweet_spot = pda_item.get('mean_threshold', pda_item.get('consequent_encroachment')) 
                    ote_705_fib_key = f'fib_{int(fib_levels_config[1]*1000):.0f}' if len(fib_levels_config) > 1 and isinstance(fib_levels_config[1], float) else None
                    ote_705_fib_val = fib_levels_calculated.get(ote_705_fib_key) if ote_705_fib_key else None

                    if pda_sweet_spot and ote_705_fib_val and abs(pda_sweet_spot - ote_705_fib_val) < current_atr_val * config.get("ote_sweet_spot_atr_tolerance", constants.OTE_SWEET_SPOT_ATR_TOLERANCE_DEFAULT): 
                        ote_score += config.get("ote_sweet_spot_alignment_bonus", constants.OTE_SWEET_SPOT_ALIGNMENT_BONUS_DEFAULT) 
        
        inducement_swept_flag, swept_inducement_obj = _check_inducement_sweep_before_ote(
            dealing_range, actual_ote_zone_low, actual_ote_zone_high, liquidity_pools,
            df_with_atr, 
            current_analysis_timestamp, config, current_atr_val
        )
        if inducement_swept_flag:
            ote_score += config.get("ote_inducement_sweep_before_ote_bonus", constants.OTE_INDUCEMENT_SWEEP_BEFORE_OTE_BONUS) 
        
        active_kz_details_for_ote = []
        if killzones_info: 
            for kz_period in killzones_info: 
                if not isinstance(kz_period, dict): continue
                if kz_period.get('period_type') == "main_killzone":
                    active_kz_details_for_ote.append(kz_period.copy())
                    ote_score += config.get("ote_killzone_confluence_bonus", constants.OTE_KILLZONE_CONFLUENCE_BONUS) 
                elif kz_period.get('period_type') == "pre_shoulder": 
                    active_kz_details_for_ote.append(kz_period.copy())
                    ote_score += config.get("ote_shoulder_kz_confluence_bonus", constants.OTE_SHOULDER_KZ_CONFLUENCE_BONUS_DEFAULT) 
        
        if confluent_pd_arrays_list or inducement_swept_flag : 
            ote_areas_details.append({ 
                'based_on_bos_choch_event_timestamp': event_data['timestamp_break'], 
                'dealing_range_details': dealing_range.copy(), 
                'ote_trade_direction': ote_trade_direction, 
                'fib_levels_in_range': fib_levels_calculated, 
                'ote_zone_min_price': round(actual_ote_zone_low, 5), 
                'ote_zone_max_price': round(actual_ote_zone_high, 5), 
                'confluent_pd_arrays_in_ote_zone': confluent_pd_arrays_list, 
                'ote_confidence_score': round(min(config.get("max_ote_confidence_score", constants.MAX_OTE_CONFIDENCE_SCORE_DEFAULT), ote_score), 1), 
                'active_killzone_periods': active_kz_details_for_ote, 
                'inducement_swept_before_ote_details': swept_inducement_obj, 
            })

    logger.info(f"Detected {len(ote_areas_details)} OTE levels (enhanced detection).")
    return sorted(ote_areas_details, key=lambda x: (-x['ote_confidence_score'], pd.to_datetime(x['based_on_bos_choch_event_timestamp'],utc=True)))

logger.info("ict_analyzer_package/ote_detector.py (enhanced version) loaded.")
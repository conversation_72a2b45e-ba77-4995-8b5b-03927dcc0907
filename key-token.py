
import os

# کلید API برای CryptoCompare (برای دریافت داده‌های قیمت)
os.environ['CRYPTOCOMPARE_API_KEY'] = '****************************************************************'

# Telegram
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['TELEGRAM_CHAT_ID'] = '-1002681284500'

# تنظیمات برای AvalAI
os.environ['AVALAI_API_KEY'] = 'aa-CdF3eYTTjj01KmgVFoNRWkT9wFhefaLAS7UEvaFFQyGTZJSu' # کلید AvalAI شما
os.environ['AVALAI_MODEL_NAME'] = 'gemini-2.5-flash-preview-04-17' # یا هر مدل دیگری که AvalAI پشتیبانی می‌کند و برای تحلیل تصویر مناسب است، مانند 'gpt-4o'
os.environ['AVALAI_BASE_URL'] = 'https://api.avalai.ir/v1' # URL پایه AvalAI (می‌توانید از https://api.avalapis.ir/v1 هم استفاده کنید اگر سرور شما در ایران است)
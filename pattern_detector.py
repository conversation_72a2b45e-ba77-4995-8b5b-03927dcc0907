# ict_analyzer_package/pattern_detector.py
"""
Detects specific ICT composite patterns like "Major Liquidity Sweep then Strong Shift".
"""
import pandas as pd
import logging
from typing import List, Dict, Optional, Tuple

from . import constants # For new pattern-specific constants

logger = logging.getLogger(__name__)

def detect_major_sweep_and_shift_patterns(
    df_with_atr: pd.DataFrame, 
    key_liquidity_pools: List[Dict], 
    bos_choch_events: List[Dict],
    cfg: Dict, # Configuration dictionary from analyzer.py
    # Column names are passed for robustness if direct df access needed beyond event/pool data
    high_col: str, 
    low_col: str, 
    close_col: str, 
    open_col: str 
) -> List[Dict]:
    """
    Detects "Major Liquidity Sweep then Strong Market Structure Shift" patterns.
    This pattern is a high-probability ICT setup precursor.
    """
    detected_patterns = []
    if df_with_atr.empty:
        logger.debug("DataFrame is empty in detect_major_sweep_and_shift_patterns.")
        return detected_patterns

    min_liq_sig = cfg.get("sweep_shift_min_liq_significance", constants.SWEEP_SHIFT_MIN_LIQ_SIGNIFICANCE_DEFAULT)
    max_time_after_sweep_minutes = cfg.get("sweep_shift_max_time_after_sweep_minutes", constants.SWEEP_SHIFT_MAX_TIME_AFTER_SWEEP_MINUTES_DEFAULT)
    max_time_delta_after_sweep = pd.Timedelta(minutes=max_time_after_sweep_minutes)
    min_shift_quality = cfg.get("sweep_shift_min_bos_choch_quality", constants.SWEEP_SHIFT_MIN_BOS_CHOCH_QUALITY_DEFAULT)

    conf_liq_weight = cfg.get("sweep_shift_pattern_conf_liq_weight", constants.SWEEP_SHIFT_PATTERN_CONF_LIQ_WEIGHT)
    conf_shift_weight = cfg.get("sweep_shift_pattern_conf_shift_weight", constants.SWEEP_SHIFT_PATTERN_CONF_SHIFT_WEIGHT)
    conf_fvg_bonus = cfg.get("sweep_shift_pattern_conf_fvg_bonus", constants.SWEEP_SHIFT_PATTERN_CONF_FVG_BONUS)

    # Sort events by time for efficient searching
    sorted_bos_choch = sorted(
        [e for e in bos_choch_events if isinstance(e, dict) and e.get('timestamp_break')],
        key=lambda x: pd.to_datetime(x['timestamp_break'], utc=True)
    )
    
    significant_swept_pools = [
        pool for pool in key_liquidity_pools
        if isinstance(pool, dict) and
           pool.get('current_status') == 'swept' and 
           pool.get('significance_score', 0) >= min_liq_sig and
           pool.get('timestamp_event') 
    ]

    if not significant_swept_pools:
        logger.debug("No significant swept liquidity pools found to check for sweep-shift patterns.")
        return detected_patterns

    for swept_pool in significant_swept_pools:
        try:
            sweep_ts = pd.to_datetime(swept_pool['timestamp_event'], utc=True)
            swept_price = swept_pool['price_level']
            pool_type = swept_pool.get('type', '') 
            
            was_high_swept = any(ht in pool_type.upper() for ht in ['H', 'HIGH', 'EQH']) 
            was_low_swept = any(lt in pool_type.upper() for lt in ['L', 'LOW', 'EQL'])   

            if not (was_high_swept or was_low_swept):
                logger.debug(f"Swept pool type '{pool_type}' for pool at {swept_price} @ {sweep_ts} is not clearly high/low for sweep-shift pattern. Skipping.")
                continue
        except Exception as e:
            logger.warning(f"Could not process swept_pool for sweep-shift pattern: {swept_pool}. Error: {e}")
            continue

        for shift_event in sorted_bos_choch:
            try:
                shift_ts = pd.to_datetime(shift_event['timestamp_break'], utc=True)
            except Exception as e:
                logger.warning(f"Could not parse shift_event timestamp: {shift_event.get('timestamp_break')}. Error: {e}")
                continue

            if shift_ts <= sweep_ts:
                continue

            if shift_ts > sweep_ts + max_time_delta_after_sweep:
                break 

            expected_shift_is_bullish = was_low_swept
            expected_shift_is_bearish = was_high_swept
            
            actual_shift_type_str = shift_event.get('type', '').lower()
            shift_quality = shift_event.get('confirmation_quality_score', 0)
            fvg_on_shift_flag = shift_event.get('fvg_created_on_break', False)
            fvg_details_shift = shift_event.get('fvg_details') if fvg_on_shift_flag else None

            is_valid_directional_shift = False
            if expected_shift_is_bullish and "bullish" in actual_shift_type_str:
                is_valid_directional_shift = True
            elif expected_shift_is_bearish and "bearish" in actual_shift_type_str:
                is_valid_directional_shift = True

            if is_valid_directional_shift and shift_quality >= min_shift_quality:
                pattern_name_detail = "BullishShiftAfterLowSweep" if expected_shift_is_bullish else "BearishShiftAfterHighSweep"
                
                confidence = (swept_pool.get('significance_score', 0) / 10.0) * conf_liq_weight + \
                             (shift_quality / 10.0) * conf_shift_weight
                if fvg_on_shift_flag and fvg_details_shift: 
                    fvg_quality_proxy = fvg_details_shift.get('importance_score', 5.0) / 10.0 
                    confidence += conf_fvg_bonus * fvg_quality_proxy
                
                confidence = round(min(10.0, confidence * 10.0), 1) # Rescale to 0-10

                pattern_details = {
                    "pattern_name": f"MajorSweepAndShift_{pattern_name_detail}",
                    "timestamp_sweep_event": sweep_ts.isoformat(),
                    "swept_liquidity_details": swept_pool.copy(),
                    "timestamp_shift_event": shift_ts.isoformat(),
                    "shift_event_details": shift_event.copy(),
                    "fvg_with_shift_details": fvg_details_shift.copy() if fvg_details_shift else None,
                    "pattern_confidence_score": confidence,
                    "time_between_sweep_and_shift_seconds": (shift_ts - sweep_ts).total_seconds()
                }
                detected_patterns.append(pattern_details)
                break 
    
    if detected_patterns:
        logger.info(f"Detected {len(detected_patterns)} Major Sweep & Shift patterns for this timeframe.")
        detected_patterns.sort(key=lambda x: (-x['pattern_confidence_score'], pd.to_datetime(x['timestamp_shift_event'], utc=True)))

    return detected_patterns

logger.info("ict_analyzer_package/pattern_detector.py loaded.")
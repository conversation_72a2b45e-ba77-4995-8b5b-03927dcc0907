# ict_analyzer_package/helpers.py
"""
توابع کمکی داخلی برای ماژول تحلیلگر ICT
Internal helper functions for the ICT analyzer module.
"""
import pandas as pd
import numpy as np
import logging
import pandas_ta as ta # pandas_ta is used here

# Import constants from within the package
from . import constants

logger = logging.getLogger(__name__)

def _calculate_true_range(high: pd.Series, low: pd.Series, prev_close: pd.Series) -> pd.Series:
    """محاسبه دستی True Range."""
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    return pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

def _calculate_manual_atr(df: pd.DataFrame, period: int) -> pd.Series:
    """محاسبه دستی ATR."""
    if not all(col in df.columns for col in ['High', 'Low', 'Close']) or len(df) < period + 1:
        logger.warning("Data insufficient for manual ATR calculation.")
        return pd.Series(dtype=float, index=df.index)

    df_copy = df.copy()
    # Ensure original case column names if they exist, otherwise assume lowercase from potential rename
    high_col = 'High' if 'High' in df_copy.columns else 'high'
    low_col = 'Low' if 'Low' in df_copy.columns else 'low'
    close_col = 'Close' if 'Close' in df_copy.columns else 'close'

    df_copy['Prev_Close'] = df_copy[close_col].shift(1)
    df_copy['TR'] = _calculate_true_range(df_copy[high_col], df_copy[low_col], df_copy['Prev_Close'])

    atr_values = [np.nan] * period
    if len(df_copy) > period:
        first_atr = df_copy['TR'].iloc[1:period+1].mean()
        atr_values.append(first_atr)

        for i in range(period + 1, len(df_copy)):
            next_atr = (atr_values[-1] * (period - 1) + df_copy['TR'].iloc[i]) / period
            atr_values.append(next_atr)
    else: # Not enough data even for the first ATR
        atr_values = [np.nan] * len(df_copy)

    return pd.Series(atr_values, index=df_copy.index)


def ensure_atr(df: pd.DataFrame, period: int = constants.ATR_PERIOD_DEFAULT, col_name: str = 'ATR_Value') -> pd.DataFrame:
    """
    محاسبه و اطمینان از وجود ستون ATR معتبر در دیتافریم.
    ابتدا از pandas_ta استفاده می‌کند، سپس محاسبه دستی و در نهایت fallback به درصدی از قیمت.
    Renamed from _ensure_atr to be callable from other modules if needed, though primarily internal.
    """
    df_copy = df.copy()
    # Ensure columns are lowercase for ta library, but work with original case if present for other ops
    df_ta_compat = df_copy.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, errors='ignore')
    # Original case for fallback logic
    close_col_orig_case = 'Close' if 'Close' in df_copy.columns else 'close'


    if col_name in df_copy.columns and df_copy[col_name].notna().all() and (df_copy[col_name] > constants.ATR_MIN_VALUE).all():
        return df_copy

    atr_calculated_successfully = False
    # 1. Try pandas_ta
    if hasattr(ta, 'atr') and not df_ta_compat.empty and len(df_ta_compat) >= period:
        try:
            atr_series_ta = ta.atr(high=df_ta_compat['high'], low=df_ta_compat['low'], close=df_ta_compat['close'], length=period)
            if atr_series_ta is not None and isinstance(atr_series_ta, pd.Series) and not atr_series_ta.empty:
                df_copy[col_name] = atr_series_ta.values # Assign values to avoid index mismatch issues
                atr_calculated_successfully = True
                logger.debug(f"ATR calculated using pandas_ta for period {period}.")
            else: # Try appending if direct call fails
                 df_ta_compat.ta.atr(length=period, append=True)
                 possible_atr_cols = [f'ATRr_{period}', f'ATR_{period}']
                 atr_col_found = next((col for col in possible_atr_cols if col in df_ta_compat.columns), None)
                 if atr_col_found:
                     df_copy[col_name] = df_ta_compat[atr_col_found].copy().values
                     atr_calculated_successfully = True
                     logger.debug(f"ATR (appended) calculated using pandas_ta for period {period}.")
        except Exception as e_ta:
            logger.warning(f"Error calculating ATR with pandas_ta: {e_ta}. Trying manual calculation.")
            pass

    # 2. Try manual calculation if pandas_ta failed or didn't produce results
    if not atr_calculated_successfully:
        # Manual calculation needs original case names if df_copy wasn't fully lowercased
        df_for_manual_atr = df_copy.rename(columns={'open':'Open', 'high':'High', 'low':'Low', 'close':'Close'}, errors='ignore')
        manual_atr_series = _calculate_manual_atr(df_for_manual_atr, period=period)
        if not manual_atr_series.empty and manual_atr_series.notna().any():
            df_copy[col_name] = manual_atr_series.values
            atr_calculated_successfully = True
            logger.debug(f"ATR calculated manually for period {period}.")
        else:
            logger.warning("Manual ATR calculation also failed to produce results.")

    # 3. Fallback and cleanup
    if not atr_calculated_successfully or col_name not in df_copy.columns or df_copy[col_name].isnull().all():
        logger.warning(f"ATR calculation failed for period {period}. Using fallback percentage of close price.")
        if close_col_orig_case in df_copy.columns and not df_copy.empty:
            df_copy[col_name] = (df_copy[close_col_orig_case] * constants.ATR_ABNORMAL_PRICE_PERCENTAGE_FALLBACK).values
        else:
            df_copy[col_name] = constants.ATR_MIN_VALUE * 10
            logger.error(f"Cannot calculate ATR fallback as '{close_col_orig_case}' column is missing. Using minimal constant.")

    if col_name in df_copy.columns:
        df_copy[col_name] = df_copy[col_name].ffill().bfill()
        fallback_value_series = (df_copy[close_col_orig_case] * constants.ATR_ABNORMAL_PRICE_PERCENTAGE_FALLBACK) if close_col_orig_case in df_copy.columns else pd.Series(constants.ATR_MIN_VALUE * 10, index=df_copy.index)
        default_minimal_fallback = pd.Series(constants.ATR_MIN_VALUE * 10, index=df_copy.index)
        effective_fallback = fallback_value_series.combine_first(default_minimal_fallback)

        df_copy[col_name] = np.where(
            df_copy[col_name].fillna(constants.ATR_MIN_VALUE) <= constants.ATR_MIN_VALUE,
            effective_fallback,
            df_copy[col_name]
        )
        df_copy[col_name] = df_copy[col_name].fillna(constants.ATR_MIN_VALUE * 10)
    else:
        df_copy[col_name] = constants.ATR_MIN_VALUE * 10
        logger.error(f"ATR column '{col_name}' was not created. Assigning a minimal constant value.")
    return df_copy

logger.info("ict_analyzer_package/helpers.py loaded.")

# api_clients.py
"""
ماژول مربوط به تعامل با APIهای خارجی (CryptoCompare, AvalAI) (نسخه بهبود یافته با Retry)
Module for interacting with external APIs (CryptoCompare, AvalAI) (Enhanced with Retries).
"""
import pathlib #
import pandas as pd #
import requests #
import time #
from datetime import datetime, timezone, timedelta #
import logging #
import pytz # For timezone handling
from typing import Optional, List, Dict, Any # Added for better type hinting

try: #
    import openai #
    # Specific OpenAI error classes for more granular handling
    from openai import APIConnectionError, RateLimitError, APIStatusError, OpenAIError
except ImportError: #
    openai = None #
    APIConnectionError = RateLimitError = APIStatusError = OpenAIError = Exception # Fallback to generic Exception

import config # For API keys, URLs, model names, and new retry configs
from utils import encode_image_to_base64 # Assuming utils.py contains this

logger = logging.getLogger(__name__) #

def fetch_ohlcv_cryptocompare(fsym: str, tsym: str, endpoint_type: str, aggregate_minutes: Optional[int],
                              history_days: int, limit_per_api_call: int, api_key: Optional[str]) -> pd.DataFrame: #
    # ... (This function remains the same as your original version from response #1) ...
    # ... (No changes were requested or seem necessary for this function based on our discussions) ...
    logger.info(f"دریافت OHLCV از CryptoCompare: {fsym}/{tsym}, نوع: {endpoint_type}, تاریخچه: {history_days} روز.")
    if not api_key:
        logger.warning("کلید API CryptoCompare ارائه نشده. داده‌ای دریافت نخواهد شد.")
        return pd.DataFrame()

    url_map = {"day": "histoday", "hour": "histohour", "minute": "histominute"}
    endpoint = url_map.get(endpoint_type)
    if not endpoint:
        logger.error(f"نوع endpoint نامعتبر: {endpoint_type}")
        return pd.DataFrame()

    all_data = []
    to_ts = int(datetime.now(timezone.utc).timestamp())
    num_calls_made = 0

    for _ in range(config.MAX_PAGINATION_ATTEMPTS): # Use config for max attempts
        if num_calls_made > 0: time.sleep(config.REQUEST_DELAY_SECONDS) # Use config for delay

        params = {'fsym': fsym, 'tsym': tsym, 'limit': limit_per_api_call, 'toTs': to_ts}
        if endpoint_type == 'minute' and aggregate_minutes: params['aggregate'] = aggregate_minutes
        if endpoint_type == 'day' and aggregate_minutes: params['aggregate'] = aggregate_minutes # Aggregate for day only makes sense if > 1

        headers = {'authorization': f'Apikey {api_key}'}

        try:
            response = requests.get(config.BASE_URL_CRYPTOCOMPARE + endpoint, params=params, headers=headers, timeout=20)
            response.raise_for_status()
            data = response.json()
            num_calls_made += 1

            if data.get("Response") == "Error" or not data.get("Data", {}).get("Data"):
                logger.warning(f"خطا یا داده خالی از CryptoCompare: {data.get('Message', 'No message')}")
                break

            ohlcv_list = data["Data"]["Data"]
            all_data.extend(ohlcv_list)

            if not ohlcv_list or len(ohlcv_list) < limit_per_api_call or \
               (datetime.now(timezone.utc) - datetime.fromtimestamp(ohlcv_list[0]['time'], tz=timezone.utc)).days >= history_days:
                break
            to_ts = ohlcv_list[0]['time'] - 1
        except requests.exceptions.RequestException as e_req:
            logger.error(f"خطای درخواست HTTP به CryptoCompare: {e_req}", exc_info=True)
            break
        except Exception as e_gen:
            logger.error(f"خطای عمومی در دریافت داده از CryptoCompare: {e_gen}", exc_info=True)
            break

    if not all_data: return pd.DataFrame()

    df = pd.DataFrame(all_data)
    df['time'] = pd.to_datetime(df['time'], unit='s', utc=True)
    df = df.set_index('time').sort_index()
    df.rename(columns={'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close', 'volumefrom': 'Volume'}, inplace=True)
    df = df[['Open', 'High', 'Low', 'Close', 'Volume']]

    # Ensure data types are correct
    for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # Filter out rows where essential price data might be zero or NaN after coercion
    df = df[(df['Open'] > 0) & (df['High'] > 0) & (df['Low'] > 0) & (df['Close'] > 0)]
    df.dropna(subset=['Open', 'High', 'Low', 'Close'], inplace=True) # Drop rows if essential prices are NaN

    logger.info(f"داده OHLCV با موفقیت برای {fsym}/{tsym} با {len(df)} رکورد دریافت و پردازش شد.")
    return df


async def analyze_charts_with_avalai_v2( #
    chart_paths_list: List[str],
    fsym: str, tsym: str,
    previous_analysis_json_str: Optional[str],
    current_ict_analysis_summary_text: Optional[str]
) -> str:
    """
    (Enhanced with Better Context Assembly and Retry Logic)
    Sends chart images and analysis context to AvalAI and returns the response.
    """
    logger.info(f"--- شروع تحلیل نمودارها با AvalAI برای {fsym}/{tsym} (v2 - Enhanced) ---") #
    if not openai: #
        logger.error("کتابخانه OpenAI بارگذاری نشده است. تحلیل با AI امکان‌پذیر نیست.") #
        return "خطا: کتابخانه OpenAI برای ارتباط با AvalAI در دسترس نیست." #
    if not config.AVALAI_API_KEY or not config.AVALAI_MODEL_NAME: #
        logger.error("کلید API یا نام مدل AvalAI در فایل config تنظیم نشده است.") #
        return "خطا: تنظیمات لازم برای ارتباط با AvalAI (کلید API یا نام مدل) موجود نیست." #

    try: #
        # Initialize client without library-level retries; we'll handle custom retries.
        client = openai.OpenAI( #
            api_key=config.AVALAI_API_KEY, #
            base_url=config.AVALAI_BASE_URL, #
            max_retries=0 # Disable default retries to implement custom logic
        )
    except Exception as e_client_init: #
        logger.error(f"خطا در مقداردهی اولیه کلاینت OpenAI/AvalAI: {e_client_init}", exc_info=True) #
        return f"خطا در مقداردهی اولیه کلاینت AvalAI: {e_client_init}" #

    image_parts = [] #
    if chart_paths_list: #
        for image_path_str in chart_paths_list: #
            try: #
                path_obj = pathlib.Path(image_path_str) #
                if path_obj.exists() and path_obj.is_file(): #
                    base64_image = encode_image_to_base64(str(path_obj)) #
                    if base64_image: #
                        image_parts.append({"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}) #
                    else: logger.warning(f"خطا در انکود کردن تصویر: {image_path_str}") #
                else: logger.warning(f"فایل تصویر یافت نشد یا فایل نیست: {image_path_str}") #
            except Exception as e_img: logger.error(f"خطا در پردازش تصویر {image_path_str}: {e_img}", exc_info=True) #

    if not image_parts: logger.warning("هیچ تصویری برای ارسال به AvalAI آماده نشد.") #

    system_prompt_final = config.SYSTEM_PROMPT_ANALYSIS_V3 #
    user_content_parts = [] #
    prompt_section_separator = "\\n\\n" + ("-" * 50) + "\\n\\n" # Enhanced separator

    # Add previous analysis with a clear header
    if previous_analysis_json_str: #
        user_content_parts.append({"type": "text", "text": f"محتوای تحلیل پیشین (برای ارجاع و ادامه تحلیل در صورت نیاز):\n{previous_analysis_json_str}"}) #
        user_content_parts.append({"type": "text", "text": prompt_section_separator}) #

    # Add current ICT summary with a clear header
    if current_ict_analysis_summary_text: #
        user_content_parts.append({"type": "text", "text": f"خلاصه تحلیل عناصر ICT فعلی (مهمترین موارد برای تحلیل شما):\n{current_ict_analysis_summary_text}"}) #
    elif not image_parts : # No images and no text summary
        logger.error("نه تصویر و نه خلاصه تحلیل ICT برای ارسال به AvalAI موجود است.")
        return "خطا: محتوایی برای ارسال به AvalAI وجود ندارد (نه تصویر و نه خلاصه متنی)."
    elif not user_content_parts and image_parts : # Only images, add a default text part
         user_content_parts.append({"type": "text", "text": "لطفا نمودارهای ضمیمه شده را تحلیل کنید و بر اساس اصول ICT، روایت بازار و یک سناریوی معاملاتی احتمالی (در صورت وجود) ارائه دهید."})


    if image_parts: user_content_parts.extend(image_parts) #

    messages_payload = [{"role": "system", "content": system_prompt_final}, {"role": "user", "content": user_content_parts}] #

    # --- Enhanced Retry Logic ---
    current_retry_delay = config.AI_INITIAL_RETRY_DELAY_SECONDS #
    for attempt in range(config.AI_MAX_RETRIES + 1): #
        try:
            logger.info(f"Attempting API call to AvalAI (Attempt {attempt + 1}/{config.AI_MAX_RETRIES + 1}) for {fsym}/{tsym}...") #
            completion_response = client.chat.completions.create( #
                model=config.AVALAI_MODEL_NAME, #
                messages=messages_payload, #
                temperature=0.7, #
                max_tokens=60000, # MASSIVELY INCREASED TO 60K - Ensures complete comprehensive ICT analysis without any truncation
                timeout=120.0 #
            )
            logger.info(f"API call to AvalAI successful for {fsym}/{tsym} on attempt {attempt + 1}.") #

            # Process successful response (logic from original function)
            if completion_response and completion_response.choices and len(completion_response.choices) > 0: #
                choice = completion_response.choices[0] #
                if choice.message and choice.message.content: #
                    response_content = choice.message.content #
                    logger.info(f"AvalAI response received successfully. Finish reason: {choice.finish_reason}. Content length: {len(response_content)}") #
                    if choice.finish_reason == "length": logger.warning(f"AvalAI response potentially truncated due to max_tokens limit. Finish reason: {choice.finish_reason}") #
                    return response_content.strip() #
                else: #
                    logger.error(f"AvalAI response empty. Finish reason: {choice.finish_reason}. Response: {str(completion_response)[:500]}...") #
                    # This specific condition might not be retryable if it's a consistent model issue for this prompt
                    return f"خطا: پاسخ از مدل {config.AVALAI_MODEL_NAME} محتوای خالی داشت (finish_reason: {choice.finish_reason})." #
            else: #
                logger.error(f"Invalid or no choices from AvalAI: {str(completion_response)[:500]}...") #
                return f"خطا: پاسخ نامعتبر یا بدون انتخاب از مدل {config.AVALAI_MODEL_NAME}." #

        except APIConnectionError as e_conn: #
            error_message = f"Connection error with AvalAI API: {e_conn}"
            logger.warning(f"{error_message} (Attempt {attempt + 1})")
            if attempt >= config.AI_MAX_RETRIES: logger.error("Max retries reached for APIConnectionError."); return f"خطا: {error_message} پس از چندین تلاش."
        except RateLimitError as e_rate: #
            error_message = f"Rate limit error from AvalAI API: {e_rate}"
            logger.warning(f"{error_message} (Attempt {attempt + 1})")
            if attempt >= config.AI_MAX_RETRIES: logger.error("Max retries reached for RateLimitError."); return f"خطا: {error_message} پس از چندین تلاش."
        except APIStatusError as e_status: #
            error_message = f"AvalAI API status error (Code: {e_status.status_code}): {e_status.response.text if e_status.response else e_status.message}"
            logger.warning(f"{error_message} (Attempt {attempt + 1})")
            if e_status.status_code not in config.AI_RETRYABLE_STATUS_CODES or attempt >= config.AI_MAX_RETRIES: #
                logger.error(f"Non-retryable APIStatusError or max retries reached. Status: {e_status.status_code}")
                return f"خطا: {error_message}"
        except OpenAIError as e_openai: # Catch other OpenAI specific errors
            error_message = f"Generic OpenAI/AvalAI API error: {e_openai}"
            logger.warning(f"{error_message} (Attempt {attempt + 1})")
            # Decide if this is retryable; often it might not be unless it's a temporary service glitch
            if attempt >= config.AI_MAX_RETRIES: logger.error("Max retries reached for OpenAIError."); return f"خطا: {error_message} پس از چندین تلاش."
        except Exception as e_gen: # General exception
            logger.error(f"Unexpected error during AvalAI API call (Attempt {attempt + 1}): {e_gen}", exc_info=True) #
            # General exceptions are usually not retryable safely
            return f"خطای غیرمنتظره در ارتباط با AvalAI: {e_gen}" #

        # If we are here, it means a retryable error occurred and we haven't exceeded max_retries
        if attempt < config.AI_MAX_RETRIES:
            logger.info(f"Waiting {current_retry_delay} seconds before next retry...")
            time.sleep(current_retry_delay)
            current_retry_delay *= config.AI_RETRY_BACKOFF_FACTOR #
        else: # Should have been caught by specific error handling if max_retries hit
            logger.error("Exited retry loop unexpectedly.")
            return "خطا: تلاش‌های متعدد برای ارتباط با AvalAI ناموفق بود."

    # This line should ideally not be reached if logic is correct, means max_retries were done without success
    logger.error("All retry attempts to AvalAI failed.")
    return "خطا: تمامی تلاش‌ها برای ارتباط با AvalAI ناموفق بودند."


logger.info("api_clients.py (enhanced with retries) loaded.") #
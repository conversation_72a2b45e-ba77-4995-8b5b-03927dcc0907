# ict_analyzer_package/constants.py
"""
ماژول ثابت‌های مورد استفاده در تحلیلگر ICT
Constants used in the ICT analyzer module.
"""
import logging

logger = logging.getLogger(__name__)

# --- Constants for Parameters ---

# ATR Settings
ATR_PERIOD_DEFAULT = 14
ATR_PERIOD = ATR_PERIOD_DEFAULT
ATR_ABNORMAL_PRICE_PERCENTAGE_FALLBACK = 0.005
ATR_MIN_VALUE = 0.00001
ATR_MIN_VALUE_PRACTICAL_SLOPE = 0.00005

# EMA Settings for Trend
SHORT_EMA_PERIOD = 20; MID_EMA_PERIOD = 50; LONG_EMA_PERIOD = 200
SHORT_EMA_P = SHORT_EMA_PERIOD
MID_EMA_P = MID_EMA_PERIOD
LONG_EMA_P = LONG_EMA_PERIOD
ADX_PERIOD = 14
ADX_P = ADX_PERIOD
ADX_THRESHOLD_STRONG_TREND = 25
ADX_STRONG_THRESH = ADX_THRESHOLD_STRONG_TREND
ADX_THRESHOLD_WEAK_TREND_OR_RANGE = 20
ADX_WEAK_THRESH = ADX_THRESHOLD_WEAK_TREND_OR_RANGE
EMA_COMPRESSION_THRESHOLD_PERCENT = 0.005
EMA_COMPRESSION_THRESH_PCT = EMA_COMPRESSION_THRESHOLD_PERCENT

# --- CONSTANTS FOR ENHANCED TREND DETECTOR ---
DEFAULT_SLOPE_CALCULATION_PERIODS = 5
SLOPE_CALC_PERIODS = DEFAULT_SLOPE_CALCULATION_PERIODS
ATR_SLOPE_THRESHOLD_FLAT = 0.03
ATR_SLOPE_FLAT_THRESH = ATR_SLOPE_THRESHOLD_FLAT
ATR_SLOPE_THRESHOLD_GENTLE = 0.10
ATR_SLOPE_GENTLE_THRESH = ATR_SLOPE_THRESHOLD_GENTLE
MIN_PRACTICAL_ATR_PERCENT_OF_PRICE = 0.0005
MIN_PRACTICAL_ATR_PCT_PRICE_FOR_SLOPE = MIN_PRACTICAL_ATR_PERCENT_OF_PRICE
# Note: ATR_MIN_VALUE_PRACTICAL_SLOPE is already defined above in ATR Settings
# For "min_abs_practical_atr_for_slope", we expect MIN_ABS_PRACTICAL_ATR_FOR_SLOPE
MIN_ABS_PRACTICAL_ATR_FOR_SLOPE = ATR_MIN_VALUE_PRACTICAL_SLOPE
VOLUME_RATIO_THRESHOLD_VERY_LOW = 0.5; VOLUME_RATIO_THRESHOLD_LOW = 0.8
VOL_RATIO_VERY_LOW = VOLUME_RATIO_THRESHOLD_VERY_LOW
VOL_RATIO_LOW = VOLUME_RATIO_THRESHOLD_LOW
VOLUME_RATIO_THRESHOLD_HIGH = 1.5; VOLUME_RATIO_THRESHOLD_VERY_HIGH = 2.0
VOL_RATIO_HIGH = VOLUME_RATIO_THRESHOLD_HIGH
VOL_RATIO_VERY_HIGH = VOLUME_RATIO_THRESHOLD_VERY_HIGH

# Swing Detection
DEFAULT_SWING_WINDOW = 10 # Corresponds to 'window' param in detect_swings
SWING_WINDOW = DEFAULT_SWING_WINDOW
SWING_FVG_IN_IMPULSE_BONUS_DEFAULT = 1.0 # Corresponds to 'fvg_impulse_bonus'
FVG_IMPULSE_BONUS = SWING_FVG_IN_IMPULSE_BONUS_DEFAULT
SWING_LIQUIDITY_SWEEP_BONUS_DEFAULT = 1.5 # Corresponds to 'sweep_bonus'
SWEEP_BONUS = SWING_LIQUIDITY_SWEEP_BONUS_DEFAULT
SWING_DISPLACEMENT_AFTER_SWEEP_BONUS_DEFAULT = 1.0 # Corresponds to 'displacement_after_sweep_bonus'
DISPLACEMENT_AFTER_SWEEP_BONUS = SWING_DISPLACEMENT_AFTER_SWEEP_BONUS_DEFAULT
MIN_REACTION_FOR_STRUCTURAL_CANDIDATE_DEFAULT = 1.5 # Corresponds to 'min_reaction_candidate'
MIN_REACTION_CANDIDATE = MIN_REACTION_FOR_STRUCTURAL_CANDIDATE_DEFAULT
# --- CONSTANTS FOR ENHANCED SWING DETECTOR ---
WEIGHT_IMPULSE_STRENGTH_DEFAULT = 1.0 # Corresponds to 'weight_impulse'
WEIGHT_IMPULSE = WEIGHT_IMPULSE_STRENGTH_DEFAULT
WEIGHT_REACTION_STRENGTH_DEFAULT = 1.2 # Corresponds to 'weight_reaction'
WEIGHT_REACTION = WEIGHT_REACTION_STRENGTH_DEFAULT
MAX_SWING_STRENGTH_SCORE_DEFAULT = 10.0 # Corresponds to 'max_score'
MAX_SCORE = MAX_SWING_STRENGTH_SCORE_DEFAULT
MIN_COMBINED_STRENGTH_FOR_STRUCTURAL_CANDIDATE_DEFAULT = 3.0 # Corresponds to 'min_combined_candidate'
MIN_COMBINED_CANDIDATE = MIN_COMBINED_STRENGTH_FOR_STRUCTURAL_CANDIDATE_DEFAULT
MIN_REACTION_AFTER_SWEEP_FOR_STRUCTURAL_CANDIDATE_DEFAULT = 1.0 # Corresponds to 'min_reaction_after_sweep_candidate'
MIN_REACTION_AFTER_SWEEP_CANDIDATE = MIN_REACTION_AFTER_SWEEP_FOR_STRUCTURAL_CANDIDATE_DEFAULT
MIN_STRENGTH_FOR_PRIOR_SWEEPABLE_SWING_DEFAULT = 4.5 # Corresponds to 'min_strength_sweepable_prior'
MIN_STRENGTH_SWEEPABLE_PRIOR = MIN_STRENGTH_FOR_PRIOR_SWEEPABLE_SWING_DEFAULT

# FVG Settings
FVG_ATR_MULTIPLIER_MIN_SIZE_DEFAULT = 0.25
ATR_MULTIPLIER_MIN_SIZE = FVG_ATR_MULTIPLIER_MIN_SIZE_DEFAULT
FVG_MIDDLE_CANDLE_STRENGTH_THRESHOLD_DEFAULT = 1.2
MIDDLE_CANDLE_STRENGTH_THRESH = FVG_MIDDLE_CANDLE_STRENGTH_THRESHOLD_DEFAULT
FVG_AT_BOS_CHOCH_BONUS_DEFAULT = 2.0
FVG_AT_BOS_CHOCH_BONUS = FVG_AT_BOS_CHOCH_BONUS_DEFAULT
FVG_UNMITIGated_IMPORTANCE_FACTOR_DEFAULT = 1.2
UNMITIGATED_FACTOR = FVG_UNMITIGated_IMPORTANCE_FACTOR_DEFAULT
FVG_PARTIALLY_MITIGATED_LOW_FILL_FACTOR_DEFAULT = 1.1
PARTIALLY_MITIGATED_LOW_FILL_FACTOR = FVG_PARTIALLY_MITIGATED_LOW_FILL_FACTOR_DEFAULT
FVG_PARTIAL_MITIGATION_THRESHOLD_PCT_DEFAULT = 30.0
PARTIAL_MITIGATION_LOW_FILL_THRESHOLD_PCT = FVG_PARTIAL_MITIGATION_THRESHOLD_PCT_DEFAULT
# --- CONSTANTS FOR ENHANCED FVG DETECTOR ---
FVG_MAX_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT = 10
MAX_MITIGATION_LOOKAHEAD = FVG_MAX_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT
FVG_POST_SWEEP_BONUS_DEFAULT = 2.0
FVG_POST_SWEEP_BONUS = FVG_POST_SWEEP_BONUS_DEFAULT
FVG_FROM_SWING_DISPLACEMENT_BONUS_DEFAULT = 1.5
FVG_FROM_SWING_DISPLACEMENT_BONUS = FVG_FROM_SWING_DISPLACEMENT_BONUS_DEFAULT
FVG_MIDDLE_CANDLE_STRENGTH_SCORE_FACTOR_DEFAULT = 0.5
MIDDLE_CANDLE_STRENGTH_SCORE_FACTOR = FVG_MIDDLE_CANDLE_STRENGTH_SCORE_FACTOR_DEFAULT
FVG_SIZE_SCORE_FACTOR_DEFAULT = 0.25
FVG_SIZE_SCORE_FACTOR = FVG_SIZE_SCORE_FACTOR_DEFAULT
MAX_FVG_IMPORTANCE_SCORE_DEFAULT = 10.0
MAX_FVG_SCORE = MAX_FVG_IMPORTANCE_SCORE_DEFAULT

# Order Block Settings
OB_DISPLACEMENT_ATR_MULTIPLIER_DEFAULT = 1.3
DISPLACEMENT_MULTIPLIER = OB_DISPLACEMENT_ATR_MULTIPLIER_DEFAULT
OB_SWEEP_BEFORE_BONUS_DEFAULT = 1.0
SWEEP_BEFORE_BONUS = OB_SWEEP_BEFORE_BONUS_DEFAULT
OB_LEAD_TO_BOS_BONUS_DEFAULT = 2.0 # Base bonus
LEAD_TO_BOS_BONUS_BASE = OB_LEAD_TO_BOS_BONUS_DEFAULT
OB_REJECTION_WICK_BODY_RATIO_DEFAULT = 2.0
REJECTION_WICK_RATIO = OB_REJECTION_WICK_BODY_RATIO_DEFAULT
OB_UNMITIGATED_STRENGTH_FACTOR_DEFAULT = 1.2
# --- CONSTANTS FOR ENHANCED ORDER BLOCK (OB) DETECTOR ---
OB_FVG_CONFLUENCE_FACTOR_DEFAULT = 0.2
FVG_AFTER_CONFLUENCE_FACTOR = OB_FVG_CONFLUENCE_FACTOR_DEFAULT
OB_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT = 15
MAX_OB_STRENGTH_SCORE_DEFAULT = 10.0
MAX_OB_SCORE = MAX_OB_STRENGTH_SCORE_DEFAULT
OB_VALIDITY_DISPLACEMENT_MULTIPLIER_RELAXED_DEFAULT = 0.6
VALIDITY_DISPLACEMENT_RELAXED_MULT = OB_VALIDITY_DISPLACEMENT_MULTIPLIER_RELAXED_DEFAULT
OB_LEAD_TO_BOS_MAX_DAYS_LOOKAHEAD_DEFAULT = 3
OB_LEAD_TO_BOS_MAX_DAYS = OB_LEAD_TO_BOS_MAX_DAYS_LOOKAHEAD_DEFAULT

# Breaker Block Settings
BB_BREAK_STRENGTH_FACTOR_DEFAULT = 0.5
BREAK_STRENGTH_FACTOR_FOR_BB_SCORE = BB_BREAK_STRENGTH_FACTOR_DEFAULT
BB_LTF_CHOCH_CONFIRMATION_SCORE_PLACEHOLDER = 1.5
# --- CONSTANTS FOR ENHANCED BREAKER BLOCK (BB) DETECTOR ---
BB_MIN_OB_STRENGTH_FOR_CANDIDATE_DEFAULT = 3.5
MIN_OB_STRENGTH_FOR_BB = BB_MIN_OB_STRENGTH_FOR_CANDIDATE_DEFAULT
BB_OB_AGE_LIMIT_DAYS_DEFAULT = 45
OB_AGE_LIMIT_DAYS_FOR_BB = BB_OB_AGE_LIMIT_DAYS_DEFAULT
FVG_ON_BREAK_BONUS_BB_DEFAULT = 1.0
FVG_ON_BREAK_BONUS_BB = FVG_ON_BREAK_BONUS_BB_DEFAULT
BB_INTERNAL_FVG_BONUS_DEFAULT = 1.5
INTERNAL_FVG_IN_BB_BONUS = BB_INTERNAL_FVG_BONUS_DEFAULT
BB_DISPLACEMENT_THROUGH_OB_FACTOR_DEFAULT = 0.5
DISPLACEMENT_THROUGH_OB_FACTOR = BB_DISPLACEMENT_THROUGH_OB_FACTOR_DEFAULT
BB_RETEST_LOOKAHEAD_CANDLES_DEFAULT = 20
RETEST_LOOKAHEAD_BB = BB_RETEST_LOOKAHEAD_CANDLES_DEFAULT
MAX_BB_STRENGTH_SCORE_DEFAULT = 10.0
MAX_BB_SCORE = MAX_BB_STRENGTH_SCORE_DEFAULT

# Liquidity Pool Settings
LIQ_AGE_BONUS_PER_30_DAYS_DEFAULT = 0.5
LIQ_BUILDUP_TOUCHES_THRESHOLD_DEFAULT = 2
LIQ_BUILDUP_BONUS_PER_TOUCH_DEFAULT = 0.3
LIQ_EQ_TOLERANCE_ATR_MULT_DEFAULT = 0.05
# --- CONSTANTS FOR ENHANCED LIQUIDITY DETECTOR ---
EQ_MAX_DAYS_BETWEEN_SWINGS_DEFAULT = 45
INDUCEMENT_MAX_ATR_DISTANCE_TO_POI_DEFAULT = 2.0
INDUCEMENT_CONFIRMED_SCORE_BONUS_DEFAULT = 2.5
LIQ_UNTAPPED_BONUS_DEFAULT = 1.5
LIQ_SWEPT_BONUS_DEFAULT = 0.5
LIQ_BROKEN_STRUCTURAL_FACTOR_DEFAULT = 0.5
LIQ_EQ_BASE_SCORE_DEFAULT = 4.0
LIQ_LEVEL_EVENT_MATCH_ATR_TOLERANCE_DEFAULT = 0.25
LIQ_LEVEL_EVENT_MATCH_DAYS_TOLERANCE_DEFAULT = 3
LIQ_PRICE_MOVED_BEYOND_ATR_MULT_DEFAULT = 0.2

# BOS/CHoCH Settings
BOS_CHOCH_DISPLACEMENT_STRENGTH_THRESHOLD_MIN_DEFAULT = 1.2
BOS_CHOCH_DISPLACEMENT_THRESHOLD_MIN = BOS_CHOCH_DISPLACEMENT_STRENGTH_THRESHOLD_MIN_DEFAULT
BOS_CHOCH_CANDLE_CLOSE_DISTANCE_WEIGHT_DEFAULT = 0.6
BOS_CHOCH_CLOSE_DISTANCE_WEIGHT = BOS_CHOCH_CANDLE_CLOSE_DISTANCE_WEIGHT_DEFAULT
BOS_CHOCH_CANDLE_BODY_WEIGHT_DEFAULT = 0.4
BOS_CHOCH_CANDLE_BODY_WEIGHT = BOS_CHOCH_CANDLE_BODY_WEIGHT_DEFAULT
BOS_CHOCH_BREAKS_CONFIRMED_STRUCTURAL_PIVOT_BONUS_DEFAULT = 2.0
BOS_CHOCH_BREAKS_CONFIRMED_PIVOT_BONUS = BOS_CHOCH_BREAKS_CONFIRMED_STRUCTURAL_PIVOT_BONUS_DEFAULT
BOS_CHOCH_FOLLOW_THROUGH_CANDLES_DEFAULT = 1
BOS_CHOCH_FOLLOW_THROUGH_CANDLES = BOS_CHOCH_FOLLOW_THROUGH_CANDLES_DEFAULT
# --- CONSTANTS FOR ENHANCED STRUCTURE (BOS/CHoCH) DETECTOR ---
BOS_CHOCH_FOLLOW_THROUGH_ATR_EXTENSION_DEFAULT = 0.3
BOS_CHOCH_FOLLOW_THROUGH_ATR_EXTENSION = BOS_CHOCH_FOLLOW_THROUGH_ATR_EXTENSION_DEFAULT
BOS_CHOCH_FOLLOW_THROUGH_SCORE_BONUS_DEFAULT = 1.5
BOS_CHOCH_FOLLOW_THROUGH_SCORE_BONUS = BOS_CHOCH_FOLLOW_THROUGH_SCORE_BONUS_DEFAULT
FVG_ON_BREAK_SCORE_MULTIPLIER_DEFAULT = 0.2
FVG_ON_BREAK_SCORE_MULTIPLIER = FVG_ON_BREAK_SCORE_MULTIPLIER_DEFAULT
ORIGIN_SWING_REACTION_WEIGHT_DEFAULT = 1.0
ORIGIN_SWING_REACTION_WEIGHT = ORIGIN_SWING_REACTION_WEIGHT_DEFAULT
ORIGIN_SWING_COMBINED_STRENGTH_WEIGHT_DEFAULT = 0.5
ORIGIN_SWING_COMBINED_STRENGTH_WEIGHT = ORIGIN_SWING_COMBINED_STRENGTH_WEIGHT_DEFAULT
ORIGIN_SWING_AGE_PENALTY_FACTOR_DEFAULT = 0.01
ORIGIN_SWING_AGE_PENALTY_FACTOR = ORIGIN_SWING_AGE_PENALTY_FACTOR_DEFAULT
STRUCTURE_ORIGIN_SWING_LOOKBACK_COUNT_DEFAULT = 15 # New for structure_detector origin swing search
STRUCTURE_ORIGIN_SWING_LOOKBACK_COUNT = STRUCTURE_ORIGIN_SWING_LOOKBACK_COUNT_DEFAULT

# OTE Settings
OTE_FIB_LEVELS_STANDARD = [0.618, 0.705, 0.79] # Value used by OTE_FIB_LEVELS_STANDARD_DEFAULT
OTE_MIN_DEALING_RANGE_ATR_MULT_DEFAULT = 3.0
OTE_PD_ARRAY_CONFLUENCE_BASE_BONUS_DEFAULT = 1.0
OTE_KILLZONE_CONFLUENCE_BONUS_DEFAULT = 1.0
OTE_INDUCEMENT_SWEEP_BEFORE_OTE_BONUS_DEFAULT = 1.5
# --- CONSTANTS FOR ENHANCED OTE DETECTOR ---
OTE_MIN_BOS_CHOCH_QUALITY_DEFAULT = 4.5
OTE_BOS_CHOCH_QUALITY_FACTOR_DEFAULT = 0.5
OTE_FIB_LEVELS_STANDARD_DEFAULT = [0.618, 0.705, 0.79]
OTE_MIN_PDA_STRENGTH_FOR_CONFLUENCE_DEFAULT = 4.0
OTE_PD_ARRAY_STRENGTH_FACTOR_DEFAULT = 0.10
OTE_SWEET_SPOT_ATR_TOLERANCE_DEFAULT = 0.20
OTE_SWEET_SPOT_ALIGNMENT_BONUS_DEFAULT = 0.75
OTE_SHOULDER_KZ_CONFLUENCE_BONUS_DEFAULT = 0.5
MAX_OTE_CONFIDENCE_SCORE_DEFAULT = 10.0

# --- KILLZONE DEFINITIONS ---
DEFAULT_KILLZONE_DEFINITIONS = [
    {"name": "Asian Killzone (ICT)", "start_hour_local": 20, "end_hour_local": 0, "tz_name": "America/New_York", "type": "ICT_Killzone", "session_base": "Asia", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 30 },
    {"name": "London Open Killzone (ICT)", "start_hour_local": 2, "end_hour_local": 5, "tz_name": "America/New_York", "type": "ICT_Killzone", "session_base": "London", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 60},
    {"name": "New York Open Killzone (ICT)", "start_hour_local": 7, "end_hour_local": 10, "tz_name": "America/New_York", "type": "ICT_Killzone", "session_base": "New_York", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 60},
    {"name": "London Close Killzone (ICT)", "start_hour_local": 10, "end_hour_local": 12, "tz_name": "America/New_York", "type": "ICT_Killzone", "session_base": "London_Close", "pre_shoulder_minutes": 30 },
    {"name": "London Silver Bullet (LOKZ)", "start_hour_local": 3, "end_hour_local": 4, "tz_name": "America/New_York", "type": "Silver_Bullet", "session_base": "London"},
    {"name": "NY AM Silver Bullet (NYOKZ)", "start_hour_local": 10, "end_hour_local": 11, "tz_name": "America/New_York", "type": "Silver_Bullet", "session_base": "New_York"},
    {"name": "NY PM Silver Bullet (LCKZ_Afternoon)", "start_hour_local": 14, "end_hour_local": 15, "tz_name": "America/New_York", "type": "Silver_Bullet", "session_base": "New_York_PM"},
    {"name": "Tokyo Session", "start_hour_local": 8, "end_hour_local": 17, "tz_name": "Asia/Tokyo", "type": "Market_Session", "session_base": "Asia", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 60},
    {"name": "London Session", "start_hour_local": 8, "end_hour_local": 17, "tz_name": "Europe/London", "type": "Market_Session", "session_base": "London", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 60},
    {"name": "New York Session", "start_hour_local": 8, "end_hour_local": 17, "tz_name": "America/New_York", "type": "Market_Session", "session_base": "New_York", "pre_shoulder_minutes": 60, "post_shoulder_minutes": 60},
]
KILLZONE_CUSTOM_DEFINITIONS = []

# --- CONSTANTS FOR MAJOR SWEEP & SHIFT PATTERN DETECTOR ---
SWEEP_SHIFT_MIN_LIQ_SIGNIFICANCE_DEFAULT = 6.0
SWEEP_SHIFT_MAX_TIME_AFTER_SWEEP_MINUTES_DEFAULT = 4 * 60 
SWEEP_SHIFT_MIN_BOS_CHOCH_QUALITY_DEFAULT = 6.0 
SWEEP_SHIFT_PATTERN_CONF_LIQ_WEIGHT = 0.3
SWEEP_SHIFT_PATTERN_CONF_SHIFT_WEIGHT = 0.5
SWEEP_SHIFT_PATTERN_CONF_FVG_BONUS = 0.2

# --- CONFIGURATION KEY LISTS FOR DYNAMIC PARAMETER PASSING IN ANALYZER.PY ---
# Keys in these lists MUST match the exact parameter names in the detector functions
TREND_DETECTOR_CFG_KEYS = ["short_ema_p", "mid_ema_p", "long_ema_p", "adx_p", "adx_strong_thresh", "adx_weak_thresh", "ema_compression_thresh_pct", "slope_calc_periods", "atr_slope_flat_thresh", "atr_slope_gentle_thresh", "min_practical_atr_pct_price_for_slope", "min_abs_practical_atr_for_slope", "vol_ratio_very_low", "vol_ratio_low", "vol_ratio_high", "vol_ratio_very_high"]
SWING_DETECTOR_CFG_KEYS = [
    "atr_period", "window", # 'window' is handled by explicit_args_map in analyzer.py
    "weight_impulse", "weight_reaction", 
    "min_reaction_candidate",           # CORRECTED: Matches param name
    "min_combined_candidate",           # CORRECTED: Matches param name
    "min_reaction_after_sweep_candidate",# CORRECTED: Matches param name
    "min_strength_sweepable_prior",     # CORRECTED: Matches param name
    "max_score",                        # CORRECTED: Matches param name
    "fvg_impulse_bonus",                # CORRECTED: Matches param name
    "sweep_bonus",                      # CORRECTED: Matches param name
    "displacement_after_sweep_bonus"    # CORRECTED: Matches param name
]
FVG_DETECTOR_CFG_KEYS = ["atr_period", "atr_multiplier_min_size", "middle_candle_strength_thresh", "max_mitigation_lookahead", "fvg_post_sweep_bonus", "fvg_from_swing_displacement_bonus", "middle_candle_strength_score_factor", "fvg_size_score_factor", "max_fvg_score", "fvg_at_bos_choch_bonus", "unmitigated_factor", "partially_mitigated_low_fill_factor", "partial_mitigation_low_fill_threshold_pct"]
STRUCTURE_DETECTOR_CFG_KEYS = ["bos_choch_displacement_threshold_min", "bos_choch_close_distance_weight", "bos_choch_candle_body_weight", "bos_choch_breaks_confirmed_pivot_bonus", "bos_choch_follow_through_candles", "bos_choch_follow_through_atr_extension", "bos_choch_follow_through_score_bonus", "fvg_on_break_score_multiplier", "origin_swing_reaction_weight", "origin_swing_combined_strength_weight", "origin_swing_age_penalty_factor", "structure_origin_swing_lookback_count"]
OB_DETECTOR_CFG_KEYS = ["displacement_multiplier", "fvg_after_confluence_factor", "sweep_before_bonus", "lead_to_bos_bonus_base", "rejection_wick_ratio", "unmitigated_factor", "mitigation_lookahead", "validity_displacement_relaxed_mult", "max_ob_score", "ob_lead_to_bos_max_days"]
BB_DETECTOR_CFG_KEYS = ["min_ob_strength_for_bb", "ob_age_limit_days_for_bb", "break_strength_factor_for_bb_score", "fvg_on_break_bonus_bb", "internal_fvg_in_bb_bonus", "displacement_through_ob_factor", "retest_lookahead_bb", "max_bb_score"]
LIQ_DETECTOR_CFG_KEYS = ["liq_age_bonus_per_30_days", "liq_buildup_touches_threshold", "liq_buildup_bonus_per_touch", "liq_eq_tolerance_atr_mult", "eq_max_days_between_swings", "inducement_max_atr_distance_to_poi", "inducement_confirmed_score_bonus", "liq_untapped_bonus", "liq_swept_bonus", "liq_broken_structural_factor", "liq_eq_base_score", "liq_level_event_match_atr_tolerance", "liq_level_event_match_days_tolerance", "liq_price_moved_beyond_atr_mult"] # atr_period is passed directly
OTE_DETECTOR_CFG_KEYS = ["ote_min_bos_choch_quality", "ote_bos_choch_quality_factor", "ote_fib_levels", "ote_min_dealing_range_atr_mult", "ote_pd_array_confluence_base_bonus", "ote_min_pda_strength_for_confluence", "ote_pd_array_strength_factor", "ote_sweet_spot_atr_tolerance", "ote_sweet_spot_alignment_bonus", "ote_inducement_sweep_before_ote_bonus", "ote_killzone_confluence_bonus", "ote_shoulder_kz_confluence_bonus", "max_ote_confidence_score"] # atr_period passed directly
SWEEP_SHIFT_DETECTOR_CFG_KEYS = ["sweep_shift_min_liq_significance", "sweep_shift_max_time_after_sweep_minutes", "sweep_shift_min_bos_choch_quality", "sweep_shift_pattern_conf_liq_weight", "sweep_shift_pattern_conf_shift_weight", "sweep_shift_pattern_conf_fvg_bonus"]
# --- END OF CONFIGURATION KEY LISTS ---

logger.info("ict_analyzer_package/constants.py fully updated with all detector constants and CORRECTED CFG_KEYS lists.")
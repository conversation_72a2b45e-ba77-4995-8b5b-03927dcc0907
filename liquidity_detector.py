# ict_analyzer_package/liquidity_detector.py
"""
شناسایی نواحی نقدینگی کلیدی (نسخه بهبود یافته)
Detects key liquidity pools with enhanced status (swept/broken), inducement logic, and scoring.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import pytz 
import logging
from typing import Tuple, List, Dict, Optional # Added for type hinting

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def _determine_liquidity_status( #
    liq_price: float, liq_type: str, 
    liq_timestamp: pd.Timestamp, 
    df_slice_after_liq: pd.DataFrame, 
    current_price: float, current_atr: float,
    bos_choch_events: list,
    cfg: dict, # Contains new constants for this helper
    high_col: str, low_col: str, close_col: str
) -> Tuple[str, Optional[pd.Timestamp]]:
    """
    Determines the status of a liquidity level: untapped, swept, broken_structural, or price_moved_beyond.
    Returns status string and timestamp of event if applicable.
    """
    status = "untapped"
    event_ts = None
    
    # Default config values if not in cfg (though analyzer.py should populate cfg)
    match_atr_tolerance = cfg.get("liq_level_event_match_atr_tolerance", constants.LIQ_LEVEL_EVENT_MATCH_ATR_TOLERANCE_DEFAULT)
    match_days_tolerance = cfg.get("liq_level_event_match_days_tolerance", constants.LIQ_LEVEL_EVENT_MATCH_DAYS_TOLERANCE_DEFAULT)
    moved_beyond_atr_mult = cfg.get("liq_price_moved_beyond_atr_mult", constants.LIQ_PRICE_MOVED_BEYOND_ATR_MULT_DEFAULT)

    # Check for structural breaks related to this liquidity level
    for event in bos_choch_events:
        if not isinstance(event, dict) or not isinstance(event.get('broken_pivot_details'), dict): continue
        
        broken_pivot_price = event['broken_pivot_details'].get('price')
        broken_pivot_ts_str = event['broken_pivot_details'].get('timestamp')
        if broken_pivot_price is None or broken_pivot_ts_str is None: continue
        broken_pivot_ts = pd.to_datetime(broken_pivot_ts_str, utc=True)
        event_break_ts = pd.to_datetime(event.get('timestamp_break'), utc=True)

        if abs(broken_pivot_price - liq_price) < current_atr * match_atr_tolerance and \
           abs((broken_pivot_ts - liq_timestamp).total_seconds()) < 3600 * 24 * match_days_tolerance:
            
            if (liq_type == 'H' and "bullish" in event.get('type','').lower() and event_break_ts > liq_timestamp) or \
               (liq_type == 'L' and "bearish" in event.get('type','').lower() and event_break_ts > liq_timestamp):
                status = "broken_structural"
                event_ts = event_break_ts
                return status, event_ts 

    # If not structurally broken, check for sweeps or price moving beyond
    if not df_slice_after_liq.empty:
        for idx_after_liq in range(len(df_slice_after_liq)):
            candle = df_slice_after_liq.iloc[idx_after_liq]
            candle_ts = df_slice_after_liq.index[idx_after_liq]

            if liq_type == 'H':
                if candle[high_col] > liq_price: 
                    if candle[close_col] < liq_price: 
                        status = "swept"; event_ts = candle_ts
                        return status, event_ts 
                    else: 
                        if current_price > liq_price + current_atr * moved_beyond_atr_mult:
                             status = "price_moved_beyond"; event_ts = candle_ts 
            elif liq_type == 'L':
                if candle[low_col] < liq_price: 
                    if candle[close_col] > liq_price: 
                        status = "swept"; event_ts = candle_ts
                        return status, event_ts 
                    else:
                        if current_price < liq_price - current_atr * moved_beyond_atr_mult:
                            status = "price_moved_beyond"; event_ts = candle_ts
            
            # If a definitive "swept" occurred, no need to check further candles for *this type* of status.
            # However, "price_moved_beyond" might still be refined by later price action or current_price.
            if status == "swept": break 
    
    if status == "price_moved_beyond": # Re-evaluate if current price is now back
        if (liq_type == 'H' and current_price < liq_price) or \
           (liq_type == 'L' and current_price > liq_price):
            status = "swept" # Likely a sweep if price is now decisively back
            # event_ts for this re-evaluation would be the current analysis time, but we use first touch time.
            # If event_ts was set during loop, keep it. If not, it implies current_price caused this.

    return status, event_ts


def find_key_liquidity_pools(
    df: pd.DataFrame,
    swings: list, 
    current_time_utc_iso: str, 
    dealing_range_details: dict | None = None, 
    atr_period: int = constants.ATR_PERIOD_DEFAULT, 
    pd_arrays_for_inducement: dict | None = None, 
    bos_choch_events: list | None = None, 
    cfg: dict = None 
) -> list: #
    # ... (Full implementation from response #27, using _determine_liquidity_status and new cfg params) ...
    liquidity_pools = []
    if df.empty: logger.warning("Dataframe is empty for liquidity pool detection."); return liquidity_pools
    config = cfg if cfg is not None else {}

    try: 
        current_time_utc = datetime.fromisoformat(current_time_utc_iso.replace("Z", "+00:00"))
        current_time_utc = current_time_utc.astimezone(pytz.utc) if current_time_utc.tzinfo else pytz.utc.localize(current_time_utc)
    except Exception: logger.error(f"Invalid ISO: {current_time_utc_iso}. Using current UTC."); current_time_utc = datetime.now(timezone.utc)

    df_with_atr = ensure_atr(df.copy(), period=atr_period)
    if not isinstance(df_with_atr.index, pd.DatetimeIndex): df_with_atr.index = pd.to_datetime(df_with_atr.index, utc=True)
    if df_with_atr.index.tzinfo is None: df_with_atr.index = df_with_atr.index.tz_localize('UTC')
    else: df_with_atr.index = df_with_atr.index.tz_convert('UTC')

    high_col = 'High' if 'High' in df_with_atr.columns else 'high'; low_col = 'Low' if 'Low' in df_with_atr.columns else 'low'
    close_col = 'Close' if 'Close' in df_with_atr.columns else 'close'; open_col = 'Open' if 'Open' in df_with_atr.columns else 'open'
    current_price = df_with_atr[close_col].iloc[-1] if not df_with_atr.empty else 0
    current_atr = df_with_atr['ATR_Value'].iloc[-1] if not df_with_atr.empty and 'ATR_Value' in df_with_atr.columns and pd.notna(df_with_atr['ATR_Value'].iloc[-1]) else constants.ATR_MIN_VALUE
    if current_atr == 0: current_atr = constants.ATR_MIN_VALUE

    bos_choch_events = bos_choch_events if bos_choch_events is not None else []
    pd_arrays = pd_arrays_for_inducement if pd_arrays_for_inducement is not None else {}
    fvgs_for_inducement = pd_arrays.get('fvgs', []); obs_for_inducement = pd_arrays.get('order_blocks', [])

    resample_periods = {'D': 'Daily', 'W-MON': 'Weekly', 'MS': 'Monthly'}
    for period_code, period_name in resample_periods.items():
        try: 
            min_data = {'D':2, 'W-MON':8, 'MS':32}.get(period_code, 35)
            if len(df_with_atr) < min_data: continue
            df_resampled = df_with_atr.resample(period_code, label='left', closed='left').agg(
                {open_col:'first', high_col:'max', low_col:'min', close_col:'last'}
            ).dropna()
            if len(df_resampled) >= 2:
                prev_period_data = df_resampled.iloc[-2]
                prev_period_ts = pd.to_datetime(prev_period_data.name, utc=True)
                age_days = (current_time_utc - prev_period_ts).days
                base_score_map = {'D': 6.0, 'W-MON': 7.0, 'MS': 8.0}
                base_score = base_score_map.get(period_code, 5.0)
                age_bonus = (age_days // 30) * config.get("liq_age_bonus_per_30_days", constants.LIQ_AGE_BONUS_PER_30_DAYS_DEFAULT)
                for liq_char, price_col_key in [('H', high_col), ('L', low_col)]:
                    liq_price_val = prev_period_data[price_col_key]
                    df_slice_for_status_check = df_with_atr[df_with_atr.index > (prev_period_ts + pd.Timedelta(days=1 if period_code=='D' else (7 if period_code=='W-MON' else 30)))] # Heuristic for start of next period
                    status, event_ts = _determine_liquidity_status(liq_price_val, liq_char, prev_period_ts, df_slice_for_status_check, current_price, current_atr, bos_choch_events, config, high_col, low_col, close_col)
                    score = base_score + age_bonus
                    if status == "untapped": score += config.get("liq_untapped_bonus", constants.LIQ_UNTAPPED_BONUS_DEFAULT)
                    elif status == "swept": score += config.get("liq_swept_bonus", constants.LIQ_SWEPT_BONUS_DEFAULT)
                    elif status == "broken_structural": score *= config.get("liq_broken_structural_factor", constants.LIQ_BROKEN_STRUCTURAL_FACTOR_DEFAULT)
                    else: score += 0.5 
                    liquidity_pools.append({'price_level': round(liq_price_val, 5), 'type': f'P{period_name[0]}{liq_char}', 'liquidity_type_specific': f'Previous_{period_name}_{"High" if liq_char=="H" else "Low"}', 'timestamp_formed': prev_period_ts.isoformat(), 'timestamp_event': pd.to_datetime(event_ts, utc=True).isoformat() if event_ts else None, 'timeframe_source': period_name, 'significance_score': round(min(10, score),1), 'is_external_range_liquidity': True, 'distance_from_current_price_atr': round(abs(liq_price_val - current_price) / current_atr, 1) if current_atr > 0 else float('inf'), 'current_status': status, 'age_days': age_days})
        except Exception as e_resample: logger.error(f"Error resampling for {period_name}: {e_resample}", exc_info=True)

    if swings:
        sorted_swings_by_ts = sorted([s for s in swings if isinstance(s,dict) and 'timestamp' in s], key=lambda x: pd.to_datetime(x['timestamp'],utc=True))
        eq_tolerance_atr_mult = config.get("liq_eq_tolerance_atr_mult", constants.LIQ_EQ_TOLERANCE_ATR_MULT_DEFAULT)
        eq_max_days_between = config.get("eq_max_days_between_swings", constants.EQ_MAX_DAYS_BETWEEN_SWINGS_DEFAULT)
        processed_eq_indices = set()
        for i in range(len(sorted_swings_by_ts)):
            if i in processed_eq_indices: continue
            s1 = sorted_swings_by_ts[i]; s1_ts = pd.to_datetime(s1['timestamp'], utc=True); equal_levels_group = [s1]
            for j in range(i + 1, len(sorted_swings_by_ts)):
                if j in processed_eq_indices: continue
                s2 = sorted_swings_by_ts[j]; s2_ts = pd.to_datetime(s2['timestamp'], utc=True)
                if s1.get('type') == s2.get('type') and abs(s1.get('price',0) - s2.get('price',0)) <= current_atr * eq_tolerance_atr_mult and (s2_ts - s1_ts).days < eq_max_days_between:
                    equal_levels_group.append(s2); processed_eq_indices.add(j)
            if len(equal_levels_group) >= config.get("liq_buildup_touches_threshold", constants.LIQ_BUILDUP_TOUCHES_THRESHOLD_DEFAULT):
                avg_price_eq = round(np.mean([s['price'] for s in equal_levels_group]), 5); latest_ts_eq = max(pd.to_datetime(s['timestamp'],utc=True) for s in equal_levels_group); age_eq = (current_time_utc - latest_ts_eq).days
                df_slice_for_eq_status = df_with_atr[df_with_atr.index > latest_ts_eq]
                status_eq, event_ts_eq = _determine_liquidity_status(avg_price_eq, s1['type'][0], latest_ts_eq, df_slice_for_eq_status, current_price, current_atr, bos_choch_events, config, high_col, low_col, close_col)
                score_eq = config.get("liq_eq_base_score", constants.LIQ_EQ_BASE_SCORE_DEFAULT) + (len(equal_levels_group) -1) * config.get("liq_buildup_bonus_per_touch", constants.LIQ_BUILDUP_BONUS_PER_TOUCH_DEFAULT)
                if status_eq == "untapped": score_eq += config.get("liq_untapped_bonus", constants.LIQ_UNTAPPED_BONUS_DEFAULT)
                elif status_eq == "swept": score_eq += config.get("liq_swept_bonus", constants.LIQ_SWEPT_BONUS_DEFAULT)
                elif status_eq == "broken_structural": score_eq *= config.get("liq_broken_structural_factor", constants.LIQ_BROKEN_STRUCTURAL_FACTOR_DEFAULT)
                liquidity_pools.append({'price_level': avg_price_eq, 'type': f"EQ{s1['type'][0]}", 'liquidity_type_specific': f'Equal_{"Highs" if s1["type"]=="SH" else "Lows"} ({len(equal_levels_group)} touches)', 'timestamp_formed': latest_ts_eq.isoformat(), 'timestamp_event': pd.to_datetime(event_ts_eq, utc=True).isoformat() if event_ts_eq else None, 'timeframe_source': 'Swings_EQ', 'significance_score': round(min(10, score_eq - age_eq*0.05),1), 'is_internal_range_liquidity': True, 'distance_from_current_price_atr': round(abs(avg_price_eq - current_price)/current_atr,1) if current_atr > 0 else float('inf'), 'current_status': status_eq, 'age_days': age_eq, 'num_touches': len(equal_levels_group)})
                processed_eq_indices.add(i)

        for swing_data in sorted_swings_by_ts:
            swing_price = swing_data.get('price'); swing_ts = pd.to_datetime(swing_data.get('timestamp'), utc=True); swing_type_char = swing_data.get('type','N/A')[0]
            df_slice_for_swing_status = df_with_atr[df_with_atr.index > swing_ts]
            status_swing, event_ts_swing = _determine_liquidity_status(swing_price, swing_type_char, swing_ts, df_slice_for_swing_status, current_price, current_atr, bos_choch_events, config, high_col, low_col, close_col)
            is_structural = swing_data.get('is_structural_confirmed', False); role = swing_data.get('role_in_structure', 'Internal'); base_swing_score = swing_data.get('combined_strength_score', 0) * 0.5
            liq_type_str = f"{role}_{swing_data.get('type','Swing')}"; is_confirmed_inducement = False
            if not is_structural and "Internal" in role and status_swing == "untapped":
                max_dist_atr = config.get("inducement_max_atr_distance_to_poi", constants.INDUCEMENT_MAX_ATR_DISTANCE_TO_POI_DEFAULT)
                pois_to_check = []
                if swing_data.get('type') == 'SL':
                    pois_to_check.extend([fvg for fvg in fvgs_for_inducement if fvg.get('type') == 'bullish' and fvg.get('mitigation_info',{}).get('status') == 'unmitigated'])
                    pois_to_check.extend([ob for ob in obs_for_inducement if ob.get('type') == 'bullish' and ob.get('mitigation_info',{}).get('status') == 'unmitigated'])
                    for poi in pois_to_check:
                        poi_top = poi.get('top_price', poi.get('end_price'))
                        if poi_top is not None and swing_price > poi_top and (swing_price - poi_top) < max_dist_atr * current_atr: is_confirmed_inducement = True; break
                elif swing_data.get('type') == 'SH':
                    pois_to_check.extend([fvg for fvg in fvgs_for_inducement if fvg.get('type') == 'bearish' and fvg.get('mitigation_info',{}).get('status') == 'unmitigated'])
                    pois_to_check.extend([ob for ob in obs_for_inducement if ob.get('type') == 'bearish' and ob.get('mitigation_info',{}).get('status') == 'unmitigated'])
                    for poi in pois_to_check:
                        poi_bottom = poi.get('bottom_price', poi.get('start_price'))
                        if poi_bottom is not None and swing_price < poi_bottom and (poi_bottom - swing_price) < max_dist_atr * current_atr: is_confirmed_inducement = True; break
            if is_confirmed_inducement:
                liq_type_str = f"Confirmed_Inducement_{swing_data.get('type','Swing')}"
                base_swing_score += config.get("inducement_confirmed_score_bonus", constants.INDUCEMENT_CONFIRMED_SCORE_BONUS_DEFAULT)
            final_score = base_swing_score
            if status_swing == "untapped": final_score += config.get("liq_untapped_bonus", constants.LIQ_UNTAPPED_BONUS_DEFAULT)
            elif status_swing == "swept": final_score += config.get("liq_swept_bonus", constants.LIQ_SWEPT_BONUS_DEFAULT)
            elif status_swing == "broken_structural": final_score *= config.get("liq_broken_structural_factor", constants.LIQ_BROKEN_STRUCTURAL_FACTOR_DEFAULT)
            age_swing = (current_time_utc - swing_ts).days
            liquidity_pools.append({'price_level': swing_price, 'type': liq_type_str, 'liquidity_type_specific': liq_type_str, 'timestamp_formed': swing_ts.isoformat(), 'timestamp_event': pd.to_datetime(event_ts_swing, utc=True).isoformat() if event_ts_swing else None, 'timeframe_source': 'Swings_Analysis', 'significance_score': round(min(10, final_score - age_swing*0.02),1), 'is_internal_range_liquidity': not is_structural, 'is_external_range_liquidity': is_structural, 'distance_from_current_price_atr': round(abs(swing_price - current_price)/current_atr,1) if current_atr > 0 else float('inf'), 'current_status': status_swing, 'age_days': age_swing, 'is_inducement_candidate': is_confirmed_inducement or (not is_structural and "Internal" in role and status_swing == "untapped"), 'original_swing_details': swing_data})

    if dealing_range_details and isinstance(dealing_range_details, dict):
        dr_high, dr_low = dealing_range_details.get('high_price'), dealing_range_details.get('low_price')
        if dr_high is not None and dr_low is not None and dr_high > dr_low:
            for pool in liquidity_pools:
                if pool['price_level'] > dr_high or pool['price_level'] < dr_low : pool['is_external_range_liquidity'] = True; pool['is_internal_range_liquidity'] = False;
                else: pool['is_internal_range_liquidity'] = True; pool['is_external_range_liquidity'] = False;
                # This secondary check was a bit redundant and potentially incorrect from original, simplified above.
                # if pool.get('is_external_range_liquidity') and dr_low <= pool['price_level'] <= dr_high:
                #     pool['is_internal_range_liquidity'] = True 
                #     pool['is_external_range_liquidity'] = False 

    final_pools_map = {}
    for pool_item in sorted(liquidity_pools, key=lambda x: (-x['significance_score'], x['age_days'])):
        ts_key_obj = pool_item.get('timestamp_formed') # Should be ISO string or datetime
        if isinstance(ts_key_obj, str): ts_key_str = ts_key_obj
        elif isinstance(ts_key_obj, (datetime, pd.Timestamp)): ts_key_str = pd.to_datetime(ts_key_obj, utc=True).isoformat()
        else: ts_key_str = str(datetime.now(timezone.utc).isoformat()) # Fallback
        
        unique_key = f"{ts_key_str}_{pool_item['price_level']:.5f}_{pool_item['type']}"
        if unique_key not in final_pools_map: final_pools_map[unique_key] = pool_item
        else:
            if pool_item['significance_score'] > final_pools_map[unique_key]['significance_score']:
                final_pools_map[unique_key] = pool_item 

    final_pools_list = list(final_pools_map.values())
    for pool_item in final_pools_list:
         pool_item['significance_score'] = round(min(10.0, pool_item.get('significance_score', 3.0)), 1)

    logger.info(f"Detected {len(final_pools_list)} key liquidity pools (enhanced detection).")
    return sorted(final_pools_list, key=lambda x: (-x['significance_score'], x['age_days']))

logger.info("ict_analyzer_package/liquidity_detector.py (enhanced version) loaded.")
# chart_agent_module.py
"""
ماژول حاوی کلاس ChartAgent برای مدیریت فرآیند تحلیل
"""
import pandas as pd
import os
import json
import logging
from datetime import datetime, timezone, timedelta
import re
from typing import Optional, Dict, Any, List 

# Import from local project modules
import config 
from utils import extract_content_parts_v2
from api_clients import fetch_ohlcv_cryptocompare, analyze_charts_with_avalai_v2
from plotting import plot_chart_with_ict_elements_v3 
from file_handlers import save_text_to_docx, save_text_to_pdf
from telegram_sender import send_to_telegram
from setup_tracker import (
    get_open_setup_for_symbol,
    track_specific_open_setup, 
    add_new_setup
)

ict_analyzer_analyze_function = None
ATR_PERIOD_DEFAULT_AGENT = 14 
logger = logging.getLogger(__name__)

try:
    from ict_analyzer_package import analyze_ict_concepts_v4, constants as ict_constants
    ict_analyzer_analyze_function = analyze_ict_concepts_v4
    ATR_PERIOD_DEFAULT_AGENT = ict_constants.ATR_PERIOD_DEFAULT
    logger.info(f"ICT Analyzer package loaded. Default ATR: {ATR_PERIOD_DEFAULT_AGENT}")
except ImportError as e_pkg:
    logger.warning(f"Failed to import ict_analyzer_package: {e_pkg}. Attempting legacy import...")
    try:
        legacy_analyzer_module_name = getattr(config, 'ICT_ANALYZER_MODULE_NAME', 'ict_analyzer_v4')
        ict_analyzer_legacy = __import__(legacy_analyzer_module_name)
        ict_analyzer_analyze_function = ict_analyzer_legacy.analyze_ict_concepts_v4
        ATR_PERIOD_DEFAULT_AGENT = getattr(ict_analyzer_legacy, 'ATR_PERIOD_DEFAULT', 14)
        logger.info(f"Legacy ICT Analyzer ({legacy_analyzer_module_name}) loaded. Default ATR: {ATR_PERIOD_DEFAULT_AGENT}")
    except (ImportError, AttributeError) as e_legacy:
        logger.critical(f"Failed to load ICT Analyzer (package or legacy): {e_legacy}")

class ChartAgent:
    def __init__(self):
        logger.info("--- ChartAgent instance created (v3.4 - with refined EVAL params & AI confidence handling) ---")
        self.chart_paths: list[str] = []
        self.fsym: str = config.DEFAULT_FSYM
        self.tsym: str = config.DEFAULT_TSYM
        self.set_currency_pair(self.fsym, self.tsym)

        self.previous_analysis_data: Optional[Dict[str, Any]] = None
        self.raw_ai_response: Optional[str] = None
        self.decision_to_post_tag: Optional[str] = None
        self.core_analysis_from_ai: Optional[str] = None
        self.admin_recommendation_from_ai: Optional[str] = None
        self.full_analysis_for_files_from_ai: Optional[str] = None

        self.all_timeframes_ict_analysis: Dict[str, Dict[str, Any]] = {}
        self.dataframes_by_timeframe: Dict[str, pd.DataFrame] = {}
        self._last_evaluation_score: float = 0.0
        self._last_evaluation_reasoning: str = "ارزیابی اولیه انجام نشده."
        
        self.eval_params = {
            "Q_ULTRA_HIGH": config.EVAL_Q_ULTRA_HIGH, "Q_HIGH": config.EVAL_Q_HIGH,
            "Q_MED_UPPER": config.EVAL_Q_MED_UPPER, "Q_MED": config.EVAL_Q_MED, "Q_LOW": config.EVAL_Q_LOW,
            "TIMEFRAME_WEIGHTS": config.EVAL_TIMEFRAME_WEIGHTS,
            "HTF_CONFLICT_PENALTY": config.EVAL_HTF_CONFLICT_PENALTY,
            "FVG_KEY_TF_SCORE_MULTIPLIER": config.EVAL_FVG_KEY_TF_SCORE_MULTIPLIER,
            "FVG_HEALTH_MULTIPLIERS": config.EVAL_FVG_HEALTH_MULTIPLIERS,
            "EVENT_HTF_ALIGN_STRONG_MULTIPLIER": config.EVAL_EVENT_HTF_ALIGN_STRONG_MULTIPLIER,
            "EVENT_HTF_ALIGN_NORMAL_MULTIPLIER": config.EVAL_EVENT_HTF_ALIGN_NORMAL_MULTIPLIER,
            "EVENT_VS_HTF_PENALTY_MULTIPLIER": config.EVAL_EVENT_VS_HTF_PENALTY_MULTIPLIER,
            "EVENT_RANGING_TF_PENALTY_MULTIPLIER": config.EVAL_EVENT_RANGING_TF_PENALTY_MULTIPLIER,
            "ULTRA_HQ_CONFLUENCE_BONUS": config.EVAL_ULTRA_HQ_CONFLUENCE_BONUS,
            "MULTI_HQ_CONFLUENCE_BONUS": config.EVAL_MULTI_HQ_CONFLUENCE_BONUS,
            "MAJOR_SWEEP_SHIFT_BONUS": config.EVAL_MAJOR_SWEEP_SHIFT_BONUS,
            "CHOPPY_MARKET_PENALTY_FACTOR": config.EVAL_CHOPPY_MARKET_PENALTY_FACTOR,
            "HTF_TRENDING_BONUS_FACTOR": config.EVAL_HTF_TRENDING_BONUS_FACTOR,
            "AI_SEND_THRESHOLD": config.AI_SEND_DECISION_THRESHOLD_V2, 
            "OVERRIDE_SEND_SCORE_PERCENTAGE": config.EVAL_OVERRIDE_SEND_SCORE_PERCENTAGE,
            "OVERRIDE_MIN_ULTRA_HQ_EVENTS": config.EVAL_OVERRIDE_MIN_ULTRA_HQ_EVENTS,
            "OVERRIDE_MIN_KEY_TFS_GOOD_FVGS": config.EVAL_OVERRIDE_MIN_KEY_TFS_GOOD_FVGS,
            "MAX_SUMMARY_ITEMS_PER_TYPE_AI": getattr(config, "EVAL_MAX_SUMMARY_ITEMS_PER_TYPE_AI", 2)
        }
        self.ai_send_threshold = self.eval_params["AI_SEND_THRESHOLD"] 

        self.plot_config = {
            "annotation_level": config.DEFAULT_PLOT_ANNOTATION_LEVEL,
            "style_config": config.DEFAULT_PLOT_STYLE_CONFIG,
            "max_elements_per_type_map": config.PLOT_MAX_ELEMENTS_PER_TYPE
        }
        logger.info(f"Plotting config loaded: Annotation Level='{self.plot_config['annotation_level']}'")

    def set_currency_pair(self, fsym: str, tsym: str):
        self.fsym = fsym.upper(); self.tsym = tsym.upper()
        new_ticker_display_name = f"{self.fsym}-{self.tsym}"
        config.update_ticker_globals(self.fsym, self.tsym, new_ticker_display_name)
        logger.info(f"Currency pair set to: {config.TICKER_DISPLAY_NAME_GLOBAL}")
        self.previous_analysis_data = None

    def _get_previous_analysis_filepath(self) -> str:
        safe_fsym = "".join(c if c.isalnum() else "_" for c in self.fsym)
        safe_tsym = "".join(c if c.isalnum() else "_" for c in self.tsym)
        
        # Use config.PREVIOUS_ANALYSIS_DIR which is updated by main.py
        prev_dir = config.PREVIOUS_ANALYSIS_DIR
        if not prev_dir or prev_dir == "previous_analysis_dir_placeholder":
            logger.warning("PREVIOUS_ANALYSIS_DIR in config not properly set. Using fallback in OUTPUT_DIR.")
            base_path = config.OUTPUT_DIR if (config.OUTPUT_DIR and config.OUTPUT_DIR != "output_dir_placeholder") else os.getcwd()
            prev_dir = os.path.join(base_path, "previous_analysis_data_ict_fallback")
            os.makedirs(prev_dir, exist_ok=True) # Ensure fallback directory exists
        return os.path.join(prev_dir, f"prev_analysis_structured_{safe_fsym}_{safe_tsym}.json")

    def _load_previous_analysis_v2(self):
        logger.info("--- Loading previous structured analysis (v2) ---") 
        filepath = self._get_previous_analysis_filepath() 
        if os.path.exists(filepath): 
            try: 
                with open(filepath, 'r', encoding='utf-8') as f: self.previous_analysis_data = json.load(f) 
                logger.info(f"Previous structured analysis loaded from '{filepath}'.") 
                if not isinstance(self.previous_analysis_data, dict) or not all(k in self.previous_analysis_data for k in ['timestamp_utc', 'core_text', 'decision_tag']): 
                    logger.warning("Previous analysis file lacks expected structure. Discarding."); self.previous_analysis_data = None 
            except json.JSONDecodeError as e: logger.error(f"Error parsing JSON from '{filepath}': {e}."); self.previous_analysis_data = None 
            except Exception as e: logger.error(f"Error reading file '{filepath}': {e}"); self.previous_analysis_data = None 
        else: logger.info(f"No previous analysis file found at '{filepath}'."); self.previous_analysis_data = None 

    def _save_core_analysis_for_next_run_v2(self):
        logger.info("--- Saving current analysis for next run (structured v2) ---")
        filepath = self._get_previous_analysis_filepath()
        analysis_to_save: Dict[str, Any] = {}
        save_needed = False
        current_utc_iso = datetime.now(timezone.utc).isoformat()

        # Decision logic to determine what to save (from response #55)
        if self.decision_to_post_tag == "[POST_THIS_ANALYSIS=YES]" and self.core_analysis_from_ai and \
           not ("خطا:" in self.core_analysis_from_ai[:100].lower() or "error:" in self.core_analysis_from_ai[:100].lower()):
            analysis_to_save = {"timestamp_utc": current_utc_iso, "core_text": self.core_analysis_from_ai, "decision_tag": self.decision_to_post_tag, "admin_note": f"AI Analysis Posted. Score: {self._last_evaluation_score:.2f}. Eval: {self._last_evaluation_reasoning}", "full_ai_response_for_reference": self.full_analysis_for_files_from_ai}; save_needed = True
        elif self.decision_to_post_tag == "[POST_THIS_ANALYSIS=NO]" and self.full_analysis_for_files_from_ai and \
             not ("خطا:" in self.full_analysis_for_files_from_ai[:100].lower() or "error:" in self.full_analysis_for_files_from_ai[:100].lower()):
            analysis_to_save = {"timestamp_utc": current_utc_iso, "core_text": self.core_analysis_from_ai if self.core_analysis_from_ai else "Public section not provided.", "decision_tag": self.decision_to_post_tag, "admin_note": f"AI Analysis NOT Posted. Score: {self._last_evaluation_score:.2f}. Eval: {self._last_evaluation_reasoning}", "full_ai_response_for_reference": self.full_analysis_for_files_from_ai}; save_needed = True
        elif not self.raw_ai_response or ("خطا:" in (self.raw_ai_response[:100] if self.raw_ai_response else "") or "error:" in (self.raw_ai_response[:100] if self.raw_ai_response else "")):
            if self.previous_analysis_data: analysis_to_save = self.previous_analysis_data.copy(); analysis_to_save["admin_note"] = (f"Prev. preserved. AI error. Old: {self.previous_analysis_data.get('admin_note', 'N/A')}"); analysis_to_save["timestamp_last_error_utc"] = current_utc_iso
            else: analysis_to_save = {"timestamp_utc": current_utc_iso, "core_text": "No valid analysis (AI error).", "decision_tag": "[POST_THIS_ANALYSIS=NO]", "admin_note": "AI error, no prev data.", "full_ai_response_for_reference": self.raw_ai_response};
            save_needed = True
        else:
            admin_note_text = f"Script decision/unhandled AI. Score: {self._last_evaluation_score:.2f}. Eval: {self._last_evaluation_reasoning}"
            if self.raw_ai_response and "[POST_THIS_ANALYSIS=NO]" in self.raw_ai_response and "تحلیل به AI ارسال نشد" in self.raw_ai_response : analysis_to_save = {"timestamp_utc": current_utc_iso, "core_text": "No new public analysis (script decision).", "decision_tag": "[POST_THIS_ANALYSIS=NO]", "admin_note": admin_note_text, "full_ai_response_for_reference": self.raw_ai_response}
            elif self.previous_analysis_data: analysis_to_save = self.previous_analysis_data.copy(); analysis_to_save["admin_note"] = (f"Prev. preserved. AI inconclusive. Old: {self.previous_analysis_data.get('admin_note', 'N/A')}"); analysis_to_save["timestamp_last_inconclusive_utc"] = current_utc_iso
            else: analysis_to_save = {"timestamp_utc": current_utc_iso, "core_text": "No conclusive analysis.", "decision_tag": "[POST_THIS_ANALYSIS=NO]", "admin_note": "AI inconclusive, no prev data.", "full_ai_response_for_reference": self.raw_ai_response}
            save_needed = True
        
        if save_needed and analysis_to_save:
            try:
                # Ensure directory exists
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                # CORRECTED: 'with open...' is on a new line, properly indented under try.
                with open(filepath, 'w', encoding='utf-8') as f: # <<< Line 136 was here or related
                    json.dump(analysis_to_save, f, ensure_ascii=False, indent=2)
                logger.info(f"Structured analysis saved/updated at '{filepath}'.")
            except Exception as e_save: # This 'except' correctly follows the 'try'
                logger.error(f"Error saving structured analysis to '{filepath}': {e_save}", exc_info=True)

    def _format_ict_element_for_summary(self, element: Dict[str, Any], el_type: str) -> Optional[str]: #
        # ... (Full helper from response #49, using self.eval_params for Q_MED, Q_HIGH etc.) ...
        p_eval = self.eval_params 
        MAX_SUMMARY_ITEMS_PER_TYPE = p_eval.get("MAX_SUMMARY_ITEMS_PER_TYPE_AI", 2)
        if not isinstance(element, dict): return None
        try:
            if el_type == "FVG":
                score = element.get('importance_score',0); mit_info = element.get('mitigation_info',{}); mit_status_raw = mit_info.get('status','unmitigated'); mit_pct = mit_info.get('mitigation_percentage',0)
                if score < p_eval.get("Q_MED", 3.5): return None
                mit_s = "U" if mit_status_raw=="unmitigated" else (f"P{mit_pct:.0f}%" if "partial" in mit_status_raw else ("F" if "full" in mit_status_raw else "Unk"))
                price_range = f"{element.get('start_price',0):.4f}-{element.get('end_price',0):.4f}"
                ts_created_str = "N/A"; 
                if ts_created_obj := element.get('timestamp_created'): 
                    try: ts_created_str = pd.to_datetime(ts_created_obj,utc=True).strftime('%H:%M')
                    except: pass # Keep N/A if parsing fails
                return f"{element.get('type','').capitalize()} FVG: {price_range} (S:{score:.1f}, {mit_s}) @ {ts_created_str}"
            elif el_type == "BOS_CHOCH":
                q_score = element.get('confirmation_quality_score',0);
                if q_score < p_eval.get("Q_MED", 3.5): return None
                fvg_c="FVG+" if element.get('fvg_created_on_break') else "NoFVG"; broke_struct="ConfSw" if element.get('broken_pivot_details',{}).get('is_structural_confirmed') else "CandSw"; disp=element.get('displacement_strength_score',0)
                ts_break_str = "N/A"
                if ts_break_obj := element.get('timestamp_break'): 
                    try: ts_break_str = pd.to_datetime(ts_break_obj,utc=True).strftime('%H:%M')
                    except: pass
                return f"{element.get('type','StructEvt')} (Q:{q_score:.1f}, D:{disp:.1f}, {fvg_c}, Brk:{broke_struct}) @ {ts_break_str}"
            elif el_type == "OB":
                score=element.get('strength_score',0);
                if score < p_eval.get("Q_MED_UPPER",5.5): return None
                mit_info=element.get('mitigation_info',{}); status_raw=mit_info.get('status','unmitigated'); level_tested=mit_info.get('level_tested','none')
                status_short = "U" if status_raw=="unmitigated" else ("V" if "violated" in status_raw else ("MT" if "mean_threshold" in level_tested else ("PB" if "proximal_body" in level_tested else ("PW" if "proximal_wick" in level_tested else "T"))))
                price_range=f"{element.get('bottom_price',0):.4f}-{element.get('top_price',0):.4f}"
                ts_created_str = "N/A"
                if ts_created_obj := element.get('timestamp_created'): 
                    try: ts_created_str = pd.to_datetime(ts_created_obj,utc=True).strftime('%H:%M')
                    except: pass
                return f"{element.get('type','').capitalize()} OB: {price_range} (S:{score:.1f}, {status_short}, {element.get('ob_specific_type','Std')}) @ {ts_created_str}"
            elif el_type == "BB":
                score=element.get('strength_score',0);
                if score < p_eval.get("Q_MED_UPPER",5.5): return None
                retest_info=element.get('retest_info',{}); retest_status=retest_info.get('status','unretested')
                retest_short="U" if retest_status=="unretested" else ("H" if "held" in retest_status else ("V" if "violated" in retest_status else "T"))
                price_range=f"{element.get('bottom_price',0):.4f}-{element.get('top_price',0):.4f}"
                ts_activated_str = "N/A"
                if ts_activated_obj := element.get('timestamp_breaker_activated'): 
                    try: ts_activated_str = pd.to_datetime(ts_activated_obj,utc=True).strftime('%H:%M')
                    except: pass
                return f"{element.get('type','').replace('_',' ').capitalize()}: {price_range} (S:{score:.1f}, Retest:{retest_short}) @ {ts_activated_str}"
            elif el_type == "Liquidity":
                score=element.get('significance_score',0); status=element.get('current_status','unknown'); liq_type=element.get('type','Liq')
                if not ("Inducement" in liq_type or "P" in liq_type or "EQ" in liq_type or score >= p_eval.get("Q_HIGH",6.8)):
                    if status != "swept": return None
                status_short="U" if status=="untapped" else ("S" if status=="swept" else ("B" if status=="broken_structural" else status[:3]))
                ts_formed_str = "N/A"
                if ts_formed_obj := element.get('timestamp_formed'): 
                    try: ts_formed_str = pd.to_datetime(ts_formed_obj,utc=True).strftime('%Y-%m-%d %H:%M')
                    except: pass
                return f"{liq_type}: {element.get('price_level',0):.4f} (S:{score:.1f}, {status_short}) @ {ts_formed_str}"
            elif el_type == "OTE":
                score=element.get('ote_confidence_score',0);
                if score < p_eval.get("Q_HIGH",6.8): return None
                zone=f"{element.get('ote_zone_min_price',0):.4f}-{element.get('ote_zone_max_price',0):.4f}"; dr_ts_str_ote = element.get('based_on_bos_choch_event_timestamp'); dr_ts_ote = pd.to_datetime(dr_ts_str_ote,utc=True).strftime('%H:%M') if dr_ts_str_ote else 'N/A' ; ind_swept="IndSwp+" if element.get('inducement_swept_before_ote_details') else "NoInd"
                return f"{element.get('ote_trade_direction','').capitalize()} OTE: {zone} (Conf:{score:.1f}, DR@{dr_ts_ote}, {ind_swept})"
            elif el_type == "SweepShiftPattern":
                score=element.get('pattern_confidence_score',0);
                if score < p_eval.get("Q_HIGH",6.8): return None
                swept_liq=element.get('swept_liquidity_details',{}); shift_evt=element.get('shift_event_details',{})
                return f"Pattern {element.get('pattern_name','SweepShift')} (C:{score:.1f}): Swp {swept_liq.get('type','liq')}@{swept_liq.get('price_level',0):.4f} -> Shft {shift_evt.get('type','')} (Q:{shift_evt.get('confirmation_quality_score',0):.1f})"
        except Exception as e: logger.error(f"Err fmt element for AI: {el_type}, {element.get('type','N/A') if isinstance(element,dict) else 'NonDict'}, Error: {e}", exc_info=False); return None
        return None

    def _prepare_ict_summary_for_ai_v2(self) -> str: #
        logger.info("Preparing enhanced ICT summary for AI (v2.1)...")
        if not self.all_timeframes_ict_analysis: return "[ICT element summary not available or analysis not performed.]"
        p_eval = self.eval_params
        MAX_SUMMARY_ITEMS = p_eval.get("MAX_SUMMARY_ITEMS_PER_TYPE_AI", 2)
        summary_parts = [f"--- Comprehensive ICT Analysis Summary for {config.TICKER_DISPLAY_NAME_GLOBAL} (Analyzer v4 Output) ---"]
        htf_trends_summary = []
        for tf_label_htf in ['1D', '4H']:
            if tf_data := self.all_timeframes_ict_analysis.get(tf_label_htf):
                if trend_info := tf_data.get('market_trend_analysis'):
                    adx_strength_text=trend_info.get('trend_strength_adx_category','N/A'); 
                    slope_s = trend_info.get('ema_slope_short','-'); slope_m = trend_info.get('ema_slope_mid','-'); slope_l = trend_info.get('ema_slope_long','-');
                    slopes=f"Slopes(S/M/L):{slope_s[0] if isinstance(slope_s, str) and slope_s else '-'}/{slope_m[0] if isinstance(slope_m, str) and slope_m else '-'}/{slope_l[0] if isinstance(slope_l, str) and slope_l else '-'}"
                    htf_trends_summary.append(f"{tf_label_htf}: {trend_info.get('overall_trend_consensus','N/A')} (ADX Str:{adx_strength_text}, {slopes})")
        if htf_trends_summary: summary_parts.append("HTF Trend Consensus: "+" | ".join(htf_trends_summary))
        else: summary_parts.append("HTF Trend Consensus: Undetermined.")
        summary_parts.append("-" * 30)
        for tf_label in config.EXPECTED_CHART_LABELS_ORDER:
            tf_results = self.all_timeframes_ict_analysis.get(tf_label)
            if not tf_results or isinstance(tf_results.get("error"),str): summary_parts.append(f"\n--- TF: {tf_label} ---\n  [ICT data unavailable: {tf_results.get('error','N/A') if tf_results else 'No data'}]"); continue
            trend_tf=tf_results.get('market_trend_analysis',{}).get('overall_trend_consensus','N/A'); atr_tf=tf_results.get('atr_latest', 0)
            summary_parts.append(f"\n--- TF: {tf_label} (Trend: {trend_tf}, ATR: {atr_tf:.5f}) ---")
            elements_to_summarize = [
                ("Major Sweep & Shift Patterns", tf_results.get('all_major_sweep_shift_patterns',[]), 'pattern_confidence_score', "SweepShiftPattern"),
                ("BOS/CHoCH", tf_results.get('bos_choch_events',[]), 'confirmation_quality_score', "BOS_CHOCH"),
                ("OTE Zones", tf_results.get('optimal_trade_entry_zones',[]), 'ote_confidence_score', "OTE"),
                ("Order Blocks", tf_results.get('order_blocks',[]), 'strength_score', "OB"),
                ("Breaker Blocks", tf_results.get('breaker_blocks',[]), 'strength_score', "BB"),
                ("FVGs", tf_results.get('fair_value_gaps',[]), 'importance_score', "FVG"), 
                ("Key Liquidity Pools", tf_results.get('key_liquidity_pools',[]), 'significance_score', "Liquidity", True)
            ]
            for name, items, sort_key, el_type_name, *special_sort_flag in elements_to_summarize:
                if items and isinstance(items, list): 
                    summary_parts.append(f"  {name} ({len(items)} total):")
                    valid_items = [i for i in items if isinstance(i,dict)] 
                    if special_sort_flag and el_type_name == "Liquidity": 
                        def sort_liq_key(p_item):
                            s_val = p_item.get('significance_score',0)
                            if "Confirmed_Inducement" in p_item.get('type',''): s_val += 100
                            if p_item.get('current_status') == 'untapped': s_val += 10
                            if p_item.get('current_status') == 'swept': s_val += 5
                            return -s_val
                        sorted_items = sorted(valid_items, key=sort_liq_key)
                    else:
                        sorted_items = sorted(valid_items, key=lambda x: -x.get(sort_key,0)) 
                    
                    count = 0
                    for item_data in sorted_items:
                        formatted_item = self._format_ict_element_for_summary(item_data, el_type_name)
                        if formatted_item: summary_parts.append(f"    - {formatted_item}"); count+=1
                        if count >= MAX_SUMMARY_ITEMS + (1 if el_type_name == "Liquidity" else 0): break 
                    if count == 0: summary_parts.append(f"    No {name} met summary criteria.")
                elif items: 
                     summary_parts.append(f"  {name}: Data format issue or no items.")
            active_kz_periods_tf = tf_results.get('market_killzones_and_shoulders',[])
            if active_kz_periods_tf: active_kz_names_display = [f"{kz.get('killzone_name')} ({kz.get('period_type')})" for kz in active_kz_periods_tf if isinstance(kz,dict)]; summary_parts.append(f"  Active Killzones/Shoulders: {', '.join(active_kz_names_display)}")
            summary_parts.append("  ---")
        summary_parts.append("-" * 30); final_summary = "\n".join(summary_parts)
        logger.debug(f"ICT Summary for AI (first 1k chars):\n{final_summary[:1000]}...")
        return final_summary

    def _evaluate_analysis_for_ai_submission_v3(self) -> tuple[bool, float, str]: #
        # ... (Full implementation from response #39, which uses self.eval_params correctly) ...
        logger.info("--- Running Enhanced AI Submission Evaluation (v3.1) ---") 
        p = self.eval_params 
        total_score = 0.0; detailed_reasons = []; self._last_evaluation_score = 0.0; self._last_evaluation_reasoning = "Evaluation not performed or no data." 
        if not self.all_timeframes_ict_analysis: self._last_evaluation_reasoning = "No ICT analysis data available for evaluation."; logger.warning(self._last_evaluation_reasoning); return False, 0.0, self._last_evaluation_reasoning 
        htf_1d_trend_analysis = self.all_timeframes_ict_analysis.get('1D', {}).get('market_trend_analysis', {}); htf_4h_trend_analysis = self.all_timeframes_ict_analysis.get('4H', {}).get('market_trend_analysis', {}) 
        htf_1d_trend_consensus = htf_1d_trend_analysis.get('overall_trend_consensus', 'ranging'); htf_4h_trend_consensus = htf_4h_trend_analysis.get('overall_trend_consensus', 'ranging') 
        htf_1d_strength = htf_1d_trend_analysis.get('trend_strength_adx_category', 'weak'); htf_4h_strength = htf_4h_trend_analysis.get('trend_strength_adx_category', 'weak') 
        htf_bias = "ranging" 
        if "strong_bullish" in htf_1d_trend_consensus and "strong_bullish" in htf_4h_trend_consensus and htf_1d_strength == "strong" and htf_4h_strength == "strong": htf_bias = "strong_bullish" 
        elif (("bullish" in htf_1d_trend_consensus and htf_1d_strength != "weak") or ("bullish" in htf_4h_trend_consensus and htf_4h_strength != "weak")) and not (("bearish" in htf_1d_trend_consensus and htf_1d_strength == "strong") or ("bearish" in htf_4h_trend_consensus and htf_4h_strength == "strong")): htf_bias = "bullish" 
        elif "strong_bearish" in htf_1d_trend_consensus and "strong_bearish" in htf_4h_trend_consensus and htf_1d_strength == "strong" and htf_4h_strength == "strong": htf_bias = "strong_bearish" 
        elif (("bearish" in htf_1d_trend_consensus and htf_1d_strength != "weak") or ("bearish" in htf_4h_trend_consensus and htf_4h_strength != "weak")) and not (("bullish" in htf_1d_trend_consensus and htf_1d_strength == "strong") or ("bullish" in htf_4h_trend_consensus and htf_4h_strength == "strong")): htf_bias = "bearish" 
        logger.info(f"HTF Bias: {htf_bias} (1D:{htf_1d_trend_consensus}/{htf_1d_strength}, 4H:{htf_4h_trend_consensus}/{htf_4h_strength})"); has_htf_bias_conflict = (("bullish" in htf_1d_trend_consensus and "bearish" in htf_4h_trend_consensus) or ("bearish" in htf_1d_trend_consensus and "bullish" in htf_4h_trend_consensus)) and (htf_1d_strength == "strong" or htf_4h_strength == "strong") 
        if has_htf_bias_conflict: total_score += p["HTF_CONFLICT_PENALTY"]; detailed_reasons.append({'reason': "HTF Bias Conflict", 'score_component': p["HTF_CONFLICT_PENALTY"], 'tf': "Overall"}) 
        overall_fvg_health_score = 0.0; num_key_tfs_with_good_fvgs = 0; key_fvg_timeframes = ['1D', '4H', '1H'] 
        for tf_label_fvg in key_fvg_timeframes: 
            tf_data_fvg = self.all_timeframes_ict_analysis.get(tf_label_fvg, {}); 
            if not tf_data_fvg or isinstance(tf_data_fvg.get("error"), str): continue 
            fvgs_in_tf = tf_data_fvg.get('fair_value_gaps', []); atr_val = tf_data_fvg.get('atr_latest', 0.0001);_ = atr_val == 0 and (atr_val := 0.0001) 
            important_open_fvgs_in_tf = [fvg for fvg in fvgs_in_tf if isinstance(fvg, dict) and fvg.get('initial_importance_score',0)>=p["Q_MED"] and (abs(fvg.get('start_price',0)-fvg.get('end_price',0))/atr_val if atr_val > 0 else 0) > 0.15 and (fvg.get('mitigation_info',{}).get('status')=='unmitigated' or (fvg.get('mitigation_info',{}).get('status')=='partially_mitigated' and fvg.get('initial_importance_score',0)>=p["Q_MED_UPPER"] and fvg.get('mitigation_info',{}).get('mitigation_percentage', 100)<50)) and (htf_bias=="ranging" or (htf_bias=="bullish" and fvg.get('type')=="bullish") or (htf_bias=="strong_bullish" and fvg.get('type')=="bullish") or (htf_bias=="bearish" and fvg.get('type')=="bearish") or (htf_bias=="strong_bearish" and fvg.get('type')=="bearish") or fvg.get('initial_importance_score',0)>=p["Q_HIGH"])] 
            if important_open_fvgs_in_tf: num_key_tfs_with_good_fvgs +=1; best_fvg_in_tf = max(important_open_fvgs_in_tf, key=lambda x: x.get('importance_score',0)); fvg_quality_factor = best_fvg_in_tf.get('importance_score', p["Q_MED"]) / p["Q_ULTRA_HIGH"]; fvg_bonus = fvg_quality_factor * p["FVG_KEY_TF_SCORE_MULTIPLIER"] * p["TIMEFRAME_WEIGHTS"].get(tf_label_fvg, 0.5); overall_fvg_health_score += fvg_bonus; detailed_reasons.append({'reason': f"KeyTF FVG S:{best_fvg_in_tf.get('importance_score',0):.1f}", 'score_component': fvg_bonus, 'timeframe': tf_label_fvg}) 
        fvg_health_multiplier = p["FVG_HEALTH_MULTIPLIERS"].get(num_key_tfs_with_good_fvgs, p["FVG_HEALTH_MULTIPLIERS"].get(0,0.5)); event_based_score_accumulator = 0.0; num_ultra_high_quality_events = 0; num_high_quality_events = 0 
        for tf_label, tf_data in self.all_timeframes_ict_analysis.items(): 
            if not tf_data or isinstance(tf_data.get("error"), str): continue; tf_weight = p["TIMEFRAME_WEIGHTS"].get(tf_label,0.1); tf_component_score=0.0; is_tf_ranging="ranging" in tf_data.get('market_trend_analysis',{}).get('overall_trend_consensus','ranging') 
            valid_bos_choch = [e for e in tf_data.get('bos_choch_events',[]) if isinstance(e,dict) and (e.get('new_dealing_range_established') or e.get('broken_pivot_details',{}).get('is_structural_confirmed'))] 
            for event in sorted(valid_bos_choch, key=lambda x: x.get('confirmation_quality_score',0), reverse=True)[:1]: 
                q=event.get('confirmation_quality_score',0); fvg=event.get('fvg_created_on_break',False); broke_conf_struct=event.get('broken_pivot_details',{}).get('is_structural_confirmed',False); disp=event.get('displacement_strength_score',0); pts=0 
                if q>=p["Q_ULTRA_HIGH"] and fvg and broke_conf_struct and disp>=3: pts=7.0; num_ultra_high_quality_events+=1 
                elif q>=p["Q_HIGH"] and fvg and broke_conf_struct: pts=5.0; num_high_quality_events+=1 
                elif q>=p["Q_MED_UPPER"] and fvg: pts=2.5; num_high_quality_events+=1 
                elif q>=p["Q_MED"] and fvg: pts=1.5 
                if broke_conf_struct and q>=p["Q_MED"]: pts+=1.8 
                ev_bull, ev_bear = "bullish" in event.get('type','').lower(), "bearish" in event.get('type','').lower() 
                if (htf_bias=="strong_bullish" and ev_bull)or(htf_bias=="strong_bearish" and ev_bear): pts*=p["EVENT_HTF_ALIGN_STRONG_MULTIPLIER"] 
                elif (htf_bias=="bullish" and ev_bull)or(htf_bias=="bearish" and ev_bear): pts*=p["EVENT_HTF_ALIGN_NORMAL_MULTIPLIER"] 
                elif htf_bias!="ranging" : pts*=p["EVENT_VS_HTF_PENALTY_MULTIPLIER"] 
                if is_tf_ranging and "BOS" in event.get('type','') and q<p["Q_HIGH"]: pts*=p["EVENT_RANGING_TF_PENALTY_MULTIPLIER"] 
                if pts>0: tf_component_score+=pts*tf_weight; detailed_reasons.append({'reason':f"Struct Evt {event.get('type')} Q:{q:.1f}",'score_component':pts*tf_weight,'tf':tf_label}) 
            valid_otes = [o for o in tf_data.get('optimal_trade_entry_zones',[]) if isinstance(o,dict)] 
            for ote in sorted(valid_otes, key=lambda x: x.get('ote_confidence_score',0), reverse=True)[:1]: 
                conf=ote.get('ote_confidence_score',0); pts=0 
                fvg_conf_ote = any(c.get('details',{}).get('importance_score',0)>=p["Q_MED"] and c.get('details',{}).get('mitigation_info',{}).get('status')=='unmitigated' for c in ote.get('confluent_pd_arrays_in_ote_zone',[]) if c.get('type')=='FVG') 
                if conf>=p["Q_ULTRA_HIGH"] and fvg_conf_ote: pts=5.0; num_ultra_high_quality_events+=1 
                elif conf>=p["Q_HIGH"] and fvg_conf_ote: pts=3.5; num_high_quality_events+=1 
                elif conf>=p["Q_MED_UPPER"] and fvg_conf_ote: pts=2.0 
                elif conf>=p["Q_MED"]: pts=0.75 
                if (htf_bias=="strong_bullish" and ote.get('ote_trade_direction')=="bullish")or(htf_bias=="strong_bearish" and ote.get('ote_trade_direction')=="bearish"): pts*=p["EVENT_HTF_ALIGN_STRONG_MULTIPLIER"] 
                active_kz_periods=ote.get('active_killzone_periods',[]); 
                if isinstance(active_kz_periods, list): 
                    for kz_active_detail in active_kz_periods: 
                        if isinstance(kz_active_detail, dict) and kz_active_detail.get('period_type')=="main_killzone": pts+=1.25 
                        elif isinstance(kz_active_detail, dict) and kz_active_detail.get('period_type')=="pre_shoulder": pts+=0.5 
                if is_tf_ranging and conf<p["Q_HIGH"]: pts*=p["EVENT_RANGING_TF_PENALTY_MULTIPLIER"] 
                if pts>0: tf_component_score+=pts*tf_weight; detailed_reasons.append({'reason':f"OTE {ote.get('ote_trade_direction')} C:{conf:.1f}",'score_component':pts*tf_weight,'tf':tf_label}) 
            event_based_score_accumulator+=tf_component_score 
        event_based_score_after_multiplier = event_based_score_accumulator * fvg_health_multiplier 
        if abs(fvg_health_multiplier-1.0)>0.01 : detailed_reasons.append({'reason':f"FVG Health x{fvg_health_multiplier:.2f}",'score_component':event_based_score_accumulator*(fvg_health_multiplier-1),'timeframe':"Overall"}) 
        total_score = overall_fvg_health_score + event_based_score_after_multiplier 
        major_sweep_shift_pattern_detected_globally = False 
        for tf_label_pattern, tf_data_pattern in self.all_timeframes_ict_analysis.items(): 
            if tf_data_pattern and isinstance(tf_data_pattern,dict) and tf_data_pattern.get('major_sweep_shift_pattern_details'): 
                major_sweep_shift_pattern_detected_globally = True; bonus_val = p["MAJOR_SWEEP_SHIFT_BONUS"]; total_score += bonus_val 
                detailed_reasons.append({'reason':f"Major Liq Sweep & Shift Pattern on {tf_label_pattern}",'score_component':bonus_val,'timeframe':tf_label_pattern,'details':tf_data_pattern['major_sweep_shift_pattern_details']}); logger.info(f"Major Liq Sweep & Shift Pattern bonus (+{bonus_val}) applied from {tf_label_pattern}."); break 
        if num_ultra_high_quality_events>=p["OVERRIDE_MIN_ULTRA_HQ_EVENTS"] and ("strong" in htf_bias or num_key_tfs_with_good_fvgs>=p["OVERRIDE_MIN_KEY_TFS_GOOD_FVGS"]): total_score+=p["ULTRA_HQ_CONFLUENCE_BONUS"]; detailed_reasons.append({'reason':"Ultra HQ Event Confluence Bonus",'score_component':p["ULTRA_HQ_CONFLUENCE_BONUS"],'timeframe':"Overall"}) 
        elif num_high_quality_events>=2 and htf_bias!="ranging" and num_key_tfs_with_good_fvgs>=1: total_score+=p["MULTI_HQ_CONFLUENCE_BONUS"]; detailed_reasons.append({'reason':f"Multi HQ ({num_high_quality_events}) Event Confluence Bonus",'score_component':p["MULTI_HQ_CONFLUENCE_BONUS"],'timeframe':"Overall"}) 
        is_market_very_choppy = "ranging" in htf_1d_trend_consensus and "ranging" in htf_4h_trend_consensus and "ranging" in self.all_timeframes_ict_analysis.get('1H',{}).get('market_trend_analysis',{}).get('overall_trend_consensus',''); is_htf_strongly_trending = "strong" in htf_bias 
        if is_market_very_choppy and total_score < self.ai_send_threshold*1.1 : penalty_amount = total_score*p["CHOPPY_MARKET_PENALTY_FACTOR"]; total_score-=penalty_amount; detailed_reasons.append({'reason':"Choppy Market Penalty",'score_component':-penalty_amount,'timeframe':"Overall"}) 
        elif is_htf_strongly_trending and total_score > self.ai_send_threshold*0.5 : bonus_amount = total_score*p["HTF_TRENDING_BONUS_FACTOR"]; total_score+=bonus_amount; detailed_reasons.append({'reason':"HTF Trending Bonus",'score_component':bonus_amount,'timeframe':"Overall"}) 
        self._last_evaluation_score = round(max(0,total_score),2) 
        final_reasons_text = [f"{entry['timeframe']}: {entry['reason']} ({entry['score_component']:.2f})" for entry in sorted(detailed_reasons, key=lambda x:abs(x['score_component']), reverse=True)[:5]] 
        self._last_evaluation_reasoning = f"Score: {self._last_evaluation_score:.2f}. Factors: {'; '.join(final_reasons_text) if final_reasons_text else 'None significant'}"; logger.info(self._last_evaluation_reasoning) 
        final_decision_to_send = self._last_evaluation_score >= self.ai_send_threshold 
        if not final_decision_to_send and self._last_evaluation_score>=self.ai_send_threshold*p["OVERRIDE_SEND_SCORE_PERCENTAGE"] and num_ultra_high_quality_events>=p["OVERRIDE_MIN_ULTRA_HQ_EVENTS"] and num_key_tfs_with_good_fvgs>=p["OVERRIDE_MIN_KEY_TFS_GOOD_FVGS"] and htf_bias!="ranging": logger.info(f"Score ({self._last_evaluation_score}) < threshold ({self.ai_send_threshold}), but AI send by override."); final_decision_to_send=True; self._last_evaluation_reasoning+=" (Sent by Override Rule)" 
        return final_decision_to_send, self._last_evaluation_score, self._last_evaluation_reasoning

    # --- _extract_setup_details_from_ai_text method (full code from response #47, includes AI confidence parsing) ---
    def _extract_setup_details_from_ai_text(self, ai_full_response_content: str, decision_tag: str | None) -> Optional[Dict[str, Any]]: #
        logger.info("Extracting setup details from AI response (including timeframe, validity, and AI confidence)...")
        if not ai_full_response_content: return None
        if decision_tag != "[POST_THIS_ANALYSIS=YES]": return None
        details = {
            "entry_suggestion_text": "N/A", "entry_price_target_min": None, "entry_price_target_max": None,
            "stop_loss_price": None, "take_profit_prices": [], "direction": None,
            "setup_timeframe": None, "validity_hours": None,
            "ai_confidence": None, "ai_confidence_reason": None # NEW
        }
        try:
            params_start_marker = "[SETUP_PARAMETERS_START]"; params_end_marker = "[SETUP_PARAMETERS_END]"
            start_index = ai_full_response_content.find(params_start_marker); end_index = ai_full_response_content.find(params_end_marker)
            if start_index != -1 and end_index != -1 and start_index < end_index:
                params_text = ai_full_response_content[start_index + len(params_start_marker):end_index].strip()
                extracted_values = {}
                for line in params_text.split('\n'):
                    line = line.strip()
                    if ':' in line:
                        key, value = line.split(':', 1); key = key.strip().upper(); value = value.strip()
                        extracted_values[key] = None if value.upper() == "N/A" or not value else value
                
                details["direction"] = extracted_values.get("SETUP_DIRECTION")
                if not (details["direction"] and details["direction"].upper() in ["BUY", "SELL"]):
                    logger.warning(f"Invalid or missing SETUP_DIRECTION: {details['direction']}")
                    return None
                details["direction"] = details["direction"].upper()
                details["setup_timeframe"] = extracted_values.get("SETUP_TIMEFRAME")
                if not details["setup_timeframe"]: logger.warning("SETUP_TIMEFRAME not provided by AI.")
                
                details["ai_confidence"] = extracted_values.get("SETUP_AI_CONFIDENCE", "N/A") # NEW
                details["ai_confidence_reason"] = extracted_values.get("SETUP_AI_CONFIDENCE_REASON", "N/A") # NEW

                validity_hours_str = extracted_values.get("SETUP_VALIDITY_HOURS")
                if validity_hours_str:
                    try: details["validity_hours"] = int(validity_hours_str)
                    except ValueError: logger.warning(f"Invalid SETUP_VALIDITY_HOURS: {validity_hours_str}"); details["validity_hours"] = None
                
                try: details["entry_price_target_min"] = float(extracted_values.get("SETUP_ENTRY_MIN"))
                except (ValueError, TypeError): details["entry_price_target_min"] = None
                try: details["entry_price_target_max"] = float(extracted_values.get("SETUP_ENTRY_MAX", str(details["entry_price_target_min"]) if details["entry_price_target_min"] is not None else None )) # Ensure string for float if using fallback
                except (ValueError, TypeError): details["entry_price_target_max"] = details["entry_price_target_min"]
                try: details["stop_loss_price"] = float(extracted_values.get("SETUP_STOP_LOSS"))
                except (ValueError, TypeError): details["stop_loss_price"] = None
                
                tps = []
                for i_tp in range(1, 4):
                    tp_val_str = extracted_values.get(f"SETUP_TAKE_PROFIT_{i_tp}")
                    if tp_val_str:
                        try: tps.append(float(tp_val_str))
                        except (ValueError, TypeError): logger.warning(f"Invalid TP value for SETUP_TAKE_PROFIT_{i_tp}: {tp_val_str}")
                if tps: details["take_profit_prices"] = tps
                details["entry_suggestion_text"] = extracted_values.get("SETUP_AI_SUGGESTION_TEXT", "N/A")

                if not all([details["direction"], details["entry_price_target_min"] is not None,
                            details["stop_loss_price"] is not None, details["take_profit_prices"]]):
                    logger.warning(f"Essential setup parameters missing after extraction: {details}")
                    return None
                logger.info(f"Setup details extracted successfully: {details}")
                return details
            else: logger.warning("Setup parameters block not found in AI response."); return None
        except Exception as e: logger.error(f"Error extracting setup details: {e}", exc_info=True); return None

    # --- run method (full code from response #55, integrating all pieces) ---
    async def run(self, source_data_mode: str = 'C', is_auto_scan_run: bool = False): #
        # ... (Full implementation from response #55, which includes the logic for:
        #      - Open setup check
        #      - Data fetching loop calling ict_analyzer_analyze_function
        #      - Calling the updated plot_chart_with_ict_elements_v3 with self.plot_config
        #      - Calling _load_previous_analysis_v2
        #      - Calling _prepare_ict_summary_for_ai_v2 (now uses self._format_ict_element_for_summary)
        #      - Calling the new _evaluate_analysis_for_ai_submission_v3
        #      - AI interaction
        #      - File saving, Telegram sending
        #      - Setup creation with expiry_timestamp_utc, setup_timeframe, AND NEW AI CONFIDENCE FIELDS
        #      - _save_core_analysis_for_next_run_v2
        #      The full code for 'run' from response #55 is extensive. I will insert this updated
        #      _extract_setup_details_from_ai_text into the ChartAgent class structure from response #55.
        #      The key change for the 'run' method is when new_setup_details is created.
        #      I will paste the full 'run' method from response #55 here again, with the
        #      new_setup_details modification for AI confidence.

        logger.info(f"================== ChartAgent Run Start: {config.TICKER_DISPLAY_NAME_GLOBAL} (Mode: {source_data_mode}) ==================") 
        if not config.LIBRARIES_LOADED_SUCCESSFULLY or not ict_analyzer_analyze_function: 
            logger.critical("Core libraries or ICT analyzer not loaded. Aborting run.") 
            return
        open_setup = get_open_setup_for_symbol(self.fsym, self.tsym) 
        if open_setup and not is_auto_scan_run: 
            logger.info(f"Open setup found for {config.TICKER_DISPLAY_NAME_GLOBAL}. Tracking ID: {open_setup.get('setup_id')}.") 
            print(f"\n--- Open setup exists for {config.TICKER_DISPLAY_NAME_GLOBAL}. Checking status... ---") 
            await track_specific_open_setup(self.fsym, self.tsym, send_to_telegram_flag=True) 
            logger.info(f"Open setup check for {config.TICKER_DISPLAY_NAME_GLOBAL} complete. Skipping new analysis.") 
            return
        elif open_setup and is_auto_scan_run: 
            logger.info(f"Open setup for {config.TICKER_DISPLAY_NAME_GLOBAL} found during auto-scan. Will track, but new analysis will also proceed.") 
        logger.info(f"No open setup blocking analysis for {config.TICKER_DISPLAY_NAME_GLOBAL}, or in auto-scan. Proceeding...") 
        if not is_auto_scan_run: print(f"\n--- No open setup for {config.TICKER_DISPLAY_NAME_GLOBAL}. Starting new analysis... ---") 
        self.chart_paths = [] 
        self.raw_ai_response = None 
        self.decision_to_post_tag = None; self.core_analysis_from_ai = None; self.admin_recommendation_from_ai = None; self.full_analysis_for_files_from_ai = None 
        self.all_timeframes_ict_analysis = {}; self.dataframes_by_timeframe = {} 
        current_time_iso_str = datetime.now(timezone.utc).isoformat() 
        if source_data_mode == 'C': 
            logger.info(f"--- Phase 1 (C): Fetching data & running ICT analysis for {config.TICKER_DISPLAY_NAME_GLOBAL} ---") 
            if not config.CRYPTOCOMPARE_API_KEY: logger.error("CryptoCompare API key not set."); return 
            for tf_label in config.EXPECTED_CHART_LABELS_ORDER: 
                tf_config_tuple = config.TIMEFRAMES_CONFIG.get(tf_label) 
                if not tf_config_tuple: logger.warning(f"No config for timeframe {tf_label}. Skipping."); continue 
                _, hist_days, cc_ep_type, cc_agg_mins, api_call_limit, swing_win_cfg, num_candles_plot_cfg = tf_config_tuple
                analyzer_tf_cfg_overrides = {"swing_window": swing_win_cfg, "atr_period": ATR_PERIOD_DEFAULT_AGENT} 
                df_ohlcv = fetch_ohlcv_cryptocompare(self.fsym, self.tsym, cc_ep_type, cc_agg_mins, hist_days, api_call_limit, config.CRYPTOCOMPARE_API_KEY) 
                if df_ohlcv.empty: self.all_timeframes_ict_analysis[tf_label] = {"error": f"No data from CryptoCompare for {tf_label}"}; continue 
                self.dataframes_by_timeframe[tf_label] = df_ohlcv.copy() 
                if ict_analyzer_analyze_function: 
                    logger.info(f"Running ICT analysis for {tf_label}...") 
                    ict_results = ict_analyzer_analyze_function(df_ohlcv, current_time_iso_str, config_params=analyzer_tf_cfg_overrides, timeframe_label=tf_label) 
                    self.all_timeframes_ict_analysis[tf_label] = ict_results 
                    if isinstance(ict_results, dict) and ict_results.get('major_sweep_shift_pattern_details'): logger.info(f"MAJOR SWEEP & SHIFT PATTERN DETECTED by analyzer on {tf_label}: {ict_results['major_sweep_shift_pattern_details']}") 
                    current_max_elements = self.plot_config["max_elements_per_type_map"].get(tf_label, self.plot_config["max_elements_per_type_map"].get("default_max", 7))
                    num_candles_to_display_tf = num_candles_plot_cfg
                    chart_file = plot_chart_with_ict_elements_v3(df_to_plot_input=df_ohlcv, timeframe_label=tf_label, base_data_info="CryptoCompare", output_dir=config.OUTPUT_DIR, num_candles_to_display=num_candles_to_display_tf, ict_analysis_results=ict_results, annotation_level=self.plot_config["annotation_level"], style_config=self.plot_config["style_config"], max_elements_per_type=current_max_elements)
                    if chart_file: self.chart_paths.append(chart_file) 
                else: self.all_timeframes_ict_analysis[tf_label] = {"error": "ICT Analyzer function not available."}
        elif source_data_mode == 'I': 
             logger.info("--- Phase 1 (I): Using uploaded chart images ---") 
             if not self.chart_paths: logger.error("In 'I' mode, chart paths were not provided."); return 
             logger.info(f"Using {len(self.chart_paths)} uploaded charts.") 
             self.all_timeframes_ict_analysis = {tf: {"info": "Analysis based on uploaded images, no programmatic ICT element detection."} for tf in config.EXPECTED_CHART_LABELS_ORDER} 
        self._load_previous_analysis_v2() 
        current_ict_summary = self._prepare_ict_summary_for_ai_v2() if source_data_mode == 'C' else "[ICT summary not generated when using uploaded images]" 
        prev_analysis_str = json.dumps(self.previous_analysis_data, ensure_ascii=False, indent=2) if self.previous_analysis_data else None 
        should_send_to_ai, eval_score, eval_reason = False, 0.0, "Evaluation not applicable or failed." 
        if source_data_mode == 'C' and self.all_timeframes_ict_analysis and not any(isinstance(v, dict) and "error" in v for v in self.all_timeframes_ict_analysis.values()): 
            should_send_to_ai, eval_score, eval_reason = self._evaluate_analysis_for_ai_submission_v3() 
        elif source_data_mode == 'I': 
            should_send_to_ai = True; eval_score = 0.0; eval_reason = "Using uploaded images; sending to AI for visual analysis." 
        ai_response_filename_for_setup = None 
        if should_send_to_ai: 
            logger.info(f"Decision: SEND to AI for {config.TICKER_DISPLAY_NAME_GLOBAL}. Score: {self._last_evaluation_score:.2f}. Reason: {self._last_evaluation_reasoning}") 
            self.raw_ai_response = await analyze_charts_with_avalai_v2(self.chart_paths, self.fsym, self.tsym, prev_analysis_str, current_ict_summary) 
            if self.raw_ai_response: 
                ai_response_filename_for_setup = f"ai_raw_response_{config.TICKER_DISPLAY_NAME_GLOBAL.replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt" 
                full_ai_response_path = os.path.join(config.OUTPUT_DIR, ai_response_filename_for_setup) 
                try: 
                    with open(full_ai_response_path, 'w', encoding='utf-8') as f_ai_resp: f_ai_resp.write(self.raw_ai_response) 
                    logger.info(f"Raw AI response saved to {full_ai_response_path}") 
                except Exception as e_save_raw: logger.error(f"Failed to save raw AI response: {e_save_raw}") 
        else: 
            logger.info(f"Decision: DO NOT SEND to AI for {config.TICKER_DISPLAY_NAME_GLOBAL}. Score: {self._last_evaluation_score:.2f}. Reason: {self._last_evaluation_reasoning}") 
            self.raw_ai_response = (f"--- Analysis Not Sent to AI ---\nSymbol: {config.TICKER_DISPLAY_NAME_GLOBAL}\nReason: {self._last_evaluation_reasoning}\nScore: {self._last_evaluation_score:.2f}\n[POST_THIS_ANALYSIS=NO]") 
        if self.raw_ai_response: 
            self.decision_to_post_tag, self.core_analysis_from_ai, self.admin_recommendation_from_ai, self.full_analysis_for_files_from_ai = extract_content_parts_v2(self.raw_ai_response) 
            if self.decision_to_post_tag == "[POST_THIS_ANALYSIS=YES]" and self.full_analysis_for_files_from_ai and not ("خطا:" in self.full_analysis_for_files_from_ai[:100].lower() or "error:" in self.full_analysis_for_files_from_ai[:100].lower()): 
                timestamp_fn = datetime.now().strftime('%Y%m%d_%H%M%S'); safe_ticker_fn = config.TICKER_DISPLAY_NAME_GLOBAL.replace('/', '_'); base_fn = f"analysis_{safe_ticker_fn}_{timestamp_fn}" 
                if config.LIBRARIES_LOADED_SUCCESSFULLY: 
                    save_text_to_docx(self.full_analysis_for_files_from_ai, self.chart_paths, config.OUTPUT_DIR, base_fn + ".docx") 
                    save_text_to_pdf(self.full_analysis_for_files_from_ai, self.chart_paths, config.OUTPUT_DIR, base_fn + ".pdf") 
            self._save_core_analysis_for_next_run_v2() 
            if self.decision_to_post_tag == "[POST_THIS_ANALYSIS=YES]" and self.core_analysis_from_ai and not ("خطا:" in self.core_analysis_from_ai[:100].lower() or "error:" in self.core_analysis_from_ai[:100].lower()): 
                logger.info(f"AI Decision: POST analysis for {config.TICKER_DISPLAY_NAME_GLOBAL} to Telegram.") 
                extracted_setup_params = self._extract_setup_details_from_ai_text(self.full_analysis_for_files_from_ai, self.decision_to_post_tag)                 
                if config.TELEGRAM_BOT_TOKEN and config.TELEGRAM_CHAT_ID: 
                    await send_to_telegram(self.core_analysis_from_ai, self.chart_paths, config.TELEGRAM_BOT_TOKEN, config.TELEGRAM_CHAT_ID) 
                    logger.info(f"Analysis for {config.TICKER_DISPLAY_NAME_GLOBAL} sent to Telegram.") 
                    if extracted_setup_params: 
                        now_utc = datetime.now(timezone.utc); creation_ts_dt = now_utc 
                        ai_validity_hours_str = extracted_setup_params.get("validity_hours"); ai_validity_hours = None
                        if ai_validity_hours_str:
                            try: ai_validity_hours = int(ai_validity_hours_str)
                            except ValueError: logger.warning(f"Invalid validity_hours: {ai_validity_hours_str}")
                        setup_tf_from_ai = extracted_setup_params.get("setup_timeframe")                         
                        final_expiry_hours = config.PENDING_SETUP_DEFAULT_FALLBACK_EXPIRY_HOURS 
                        if ai_validity_hours is not None: final_expiry_hours = ai_validity_hours
                        elif setup_tf_from_ai and setup_tf_from_ai in config.SETUP_DEFAULT_EXPIRY_HOURS_BY_TIMEFRAME:
                            final_expiry_hours = config.SETUP_DEFAULT_EXPIRY_HOURS_BY_TIMEFRAME[setup_tf_from_ai]
                        elif setup_tf_from_ai: final_expiry_hours = config.SETUP_DEFAULT_EXPIRY_HOURS_BY_TIMEFRAME.get("default", config.PENDING_SETUP_DEFAULT_FALLBACK_EXPIRY_HOURS)
                        expiry_ts_dt = creation_ts_dt + timedelta(hours=final_expiry_hours)
                        new_setup_details = { 
                            "setup_id": f"{self.fsym}{self.tsym}_{now_utc.strftime('%Y%m%d_%H%M%S%f')}", 
                            "symbol_fsym": self.fsym, "symbol_tsym": self.tsym, 
                            "ticker_display_name": config.TICKER_DISPLAY_NAME_GLOBAL, 
                            "direction": extracted_setup_params.get("direction"), 
                            "entry_suggestion_text": extracted_setup_params.get("entry_suggestion_text", "N/A"), 
                            "entry_price_target_min": extracted_setup_params.get("entry_price_target_min"), 
                            "entry_price_target_max": extracted_setup_params.get("entry_price_target_max"), 
                            "stop_loss_price": extracted_setup_params.get("stop_loss_price"), 
                            "take_profit_prices": extracted_setup_params.get("take_profit_prices", []), 
                            "status": "pending_entry", 
                            "creation_timestamp_utc": creation_ts_dt.isoformat(), 
                            "expiry_timestamp_utc": expiry_ts_dt.isoformat(), 
                            "setup_timeframe": setup_tf_from_ai, 
                            "ai_confidence": extracted_setup_params.get("ai_confidence", "N/A"), 
                            "ai_confidence_reason": extracted_setup_params.get("ai_confidence_reason", "N/A"), 
                            "activation_timestamp_utc": None, "closed_timestamp_utc": None, 
                            "last_checked_timestamp_utc": creation_ts_dt.isoformat(), 
                            "current_tp_level_reached": 0, "pnl_at_close": None, "closing_price": None, 
                            "notes_tracker": [f"Created by AI at {creation_ts_dt.strftime('%Y-%m-%d %H:%M UTC')}, Setup TF: {setup_tf_from_ai or 'N/A'}, Expires: {expiry_ts_dt.strftime('%Y-%m-%d %H:%M UTC')}, AI Conf: {extracted_setup_params.get('ai_confidence', 'N/A')}"], 
                            "ai_raw_response_filename": ai_response_filename_for_setup 
                        }
                        add_new_setup(new_setup_details) 
                        logger.info(f"New setup for {config.TICKER_DISPLAY_NAME_GLOBAL} with expiry & AI confidence added.") 
                    else: logger.warning(f"No setup parameters extracted for {config.TICKER_DISPLAY_NAME_GLOBAL}.") 
                else: logger.warning("Telegram not configured. Cannot send analysis.") 
            else: logger.info(f"Analysis for {config.TICKER_DISPLAY_NAME_GLOBAL} not sent to Telegram. AI Tag: {self.decision_to_post_tag}") 
        else: 
            logger.error(f"No valid response from AvalAI for {config.TICKER_DISPLAY_NAME_GLOBAL}.") 
            self._save_core_analysis_for_next_run_v2() 
        logger.info(f"================== ChartAgent Run End: {config.TICKER_DISPLAY_NAME_GLOBAL} ==================") 

logger.info("chart_agent_module.py (v3.4 with AI confidence and refined EVAL params) loaded.")
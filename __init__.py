# Root package __init__.py
"""
Root package for the ICT Technical Analysis application.
"""
import logging

logger = logging.getLogger(__name__)
logger.info("Root package initialized.")

# Configure a logger for the package if not already configured by the main application
# This helps if the package modules are used independently.
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    # Set a default level for the package logger; can be overridden by application's root logger settings.
    logger.setLevel(logging.INFO)

logger.info("ict_analyzer_package (v_final_all_enhancements) initialized.")

__all__ = [
    'analyze_ict_concepts_v4',
    'determine_market_trend',
    'detect_swings',
    'find_fvgs',
    'find_bos_choch',
    'find_order_blocks',
    'find_breaker_blocks',
    'find_key_liquidity_pools',
    'get_market_killzones',
    'find_ote_levels',
    'detect_major_sweep_and_shift_patterns', # Export new function
    'ATR_PERIOD_DEFAULT', # Example of exporting a constant
    'TREND_DETECTOR_CFG_KEYS',
    'SWING_DETECTOR_CFG_KEYS',
    'FVG_DETECTOR_CFG_KEYS',
    'STRUCTURE_DETECTOR_CFG_KEYS',
    'OB_DETECTOR_CFG_KEYS',
    'BB_DETECTOR_CFG_KEYS',
    'LIQ_DETECTOR_CFG_KEYS',
    'OTE_DETECTOR_CFG_KEYS',
    'SWEEP_SHIFT_DETECTOR_CFG_KEYS',
]
# ict_analyzer_package/bb_detector.py
"""
شناسایی بریکر بلاک‌ها (Breaker Blocks) (نسخه بهبود یافته)
Detects Breaker Blocks (BBs) with enhanced break definition, retest analysis, and FVG confluence.
"""
import pandas as pd
import logging
import numpy as np
from typing import Tuple, List, Dict, Optional

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def _check_bb_retest_and_outcome(bb_dict: dict, df_with_atr: pd.DataFrame,
                                 bb_activation_candle_idx: int,
                                 max_lookahead: int, close_col: str, high_col: str, low_col: str) -> dict:
    """
    Checks if the Breaker Block was retested after its formation and the outcome.
    Updates 'retest_info' and 'mitigation_status' in bb_dict.
    """
    retest_info = {
        "status": "unretested", 
        "retest_candle_ts": None,
        "num_retests": 0
    }
    
    bb_top = bb_dict['top_price'] 
    bb_bottom = bb_dict['bottom_price'] 
    bb_type = bb_dict['type'] 

    for k in range(bb_activation_candle_idx + 1, min(len(df_with_atr), bb_activation_candle_idx + 1 + max_lookahead)):
        retest_candle = df_with_atr.iloc[k]
        retest_candle_ts_obj = df_with_atr.index[k] # Keep as datetime for comparison

        if bb_type == 'bullish_breaker': 
            if retest_candle[low_col] <= bb_top and retest_candle[high_col] >= bb_bottom: 
                retest_info['num_retests'] += 1
                if retest_info['status'] == "unretested": 
                    retest_info['status'] = "retested_held" 
                    retest_info['retest_candle_ts'] = retest_candle_ts_obj.isoformat()
                
                if retest_candle[close_col] < bb_bottom:
                    retest_info['status'] = "retested_violated_close_beyond"
                    retest_info['retest_candle_ts'] = retest_candle_ts_obj.isoformat() 
                    break 
        
        elif bb_type == 'bearish_breaker': 
            if retest_candle[high_col] >= bb_bottom and retest_candle[low_col] <= bb_top: 
                retest_info['num_retests'] += 1
                if retest_info['status'] == "unretested": 
                    retest_info['status'] = "retested_held"
                    retest_info['retest_candle_ts'] = retest_candle_ts_obj.isoformat()

                if retest_candle[close_col] > bb_top:
                    retest_info['status'] = "retested_violated_close_beyond"
                    retest_info['retest_candle_ts'] = retest_candle_ts_obj.isoformat()
                    break 
    
    bb_dict['retest_info'] = retest_info
    bb_dict['mitigation_status'] = retest_info['status'] 
    return bb_dict

def find_breaker_blocks(
    df: pd.DataFrame,
    order_blocks: list, 
    bos_choch_events: list, 
    fvgs: list, 
    atr_period: int = constants.ATR_PERIOD_DEFAULT, 
    # Configurable parameters using corrected constant names for defaults
    min_ob_strength_for_bb: float = constants.BB_MIN_OB_STRENGTH_FOR_CANDIDATE_DEFAULT, 
    ob_age_limit_days_for_bb: int = constants.BB_OB_AGE_LIMIT_DAYS_DEFAULT, 
    break_strength_factor_for_bb_score: float = constants.BB_BREAK_STRENGTH_FACTOR_DEFAULT, # CORRECTED
    fvg_on_break_bonus_bb: float = constants.FVG_ON_BREAK_BONUS_BB_DEFAULT, 
    internal_fvg_in_bb_bonus: float = constants.BB_INTERNAL_FVG_BONUS_DEFAULT, 
    displacement_through_ob_factor: float = constants.BB_DISPLACEMENT_THROUGH_OB_FACTOR_DEFAULT, 
    retest_lookahead_bb: int = constants.BB_RETEST_LOOKAHEAD_CANDLES_DEFAULT, 
    max_bb_score: float = constants.MAX_BB_STRENGTH_SCORE_DEFAULT 
) -> list: #
    breaker_blocks_details = []
    if df.empty or not order_blocks or not bos_choch_events: 
        logger.debug("Data, Order Blocks, or BOS/CHoCH events insufficient for Breaker Block detection.")
        return breaker_blocks_details

    df_with_atr = ensure_atr(df.copy(), period=atr_period) 
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all():
        logger.error("ATR_Value column missing/invalid in find_breaker_blocks.")
        df_with_atr['ATR_Value'] = constants.ATR_MIN_VALUE * 10

    high_col = 'High' if 'High' in df_with_atr.columns else 'high'
    low_col = 'Low' if 'Low' in df_with_atr.columns else 'low'
    close_col = 'Close' if 'Close' in df_with_atr.columns else 'close'
    open_col = 'Open' if 'Open' in df_with_atr.columns else 'open'

    mean_atr_fallback = df_with_atr['ATR_Value'].mean()
    if not pd.notna(mean_atr_fallback) or mean_atr_fallback <= constants.ATR_MIN_VALUE:
         mean_atr_fallback = max(constants.ATR_MIN_VALUE * 10, (df_with_atr[high_col] - df_with_atr[low_col]).mean() * 0.1 if not (df_with_atr[high_col] - df_with_atr[low_col]).empty else constants.ATR_MIN_VALUE * 10)

    # Ensure timestamps in order_blocks are datetime objects for sorting and comparison
    for ob in order_blocks:
        if isinstance(ob, dict) and isinstance(ob.get('timestamp_created'), str):
            ob['timestamp_created_dt'] = pd.to_datetime(ob['timestamp_created'], utc=True)
        elif isinstance(ob, dict) and isinstance(ob.get('timestamp_created'), pd.Timestamp):
            ob['timestamp_created_dt'] = ob.get('timestamp_created').tz_convert('UTC') if ob.get('timestamp_created').tzinfo else pytz.utc.localize(ob.get('timestamp_created'))


    candidate_obs = sorted(
        [ob for ob in order_blocks if isinstance(ob, dict) and
         ob.get('strength_score',0) >= min_ob_strength_for_bb and
         not ("violated" in ob.get('mitigation_info', {}).get('status', '')) and 
         ob.get('timestamp_created_dt') is not None and # Ensure datetime object exists
         (df_with_atr.index[-1] - ob['timestamp_created_dt']).days < ob_age_limit_days_for_bb],
        key=lambda x: x['timestamp_created_dt'] # Sort by datetime object
    )
    
    # Ensure timestamps in bos_choch_events are datetime objects
    for bc in bos_choch_events:
        if isinstance(bc, dict) and isinstance(bc.get('timestamp_break'), str):
            bc['timestamp_break_dt'] = pd.to_datetime(bc['timestamp_break'], utc=True)
        elif isinstance(bc, dict) and isinstance(bc.get('timestamp_break'), pd.Timestamp):
            bc['timestamp_break_dt'] = bc.get('timestamp_break').tz_convert('UTC') if bc.get('timestamp_break').tzinfo else pytz.utc.localize(bc.get('timestamp_break'))


    sorted_bos_choch = sorted([bc for bc in bos_choch_events if isinstance(bc,dict) and bc.get('timestamp_break_dt')], 
                              key=lambda x: x['timestamp_break_dt'])


    for ob_data in candidate_obs:
        ob_ts_created = ob_data['timestamp_created_dt'] # Use the datetime object
        ob_top_price, ob_bottom_price = ob_data['top_price'], ob_data['bottom_price']
        ob_range = ob_top_price - ob_bottom_price
        if ob_range <= 0: ob_range = constants.ATR_MIN_VALUE 

        for bc_event_data in sorted_bos_choch:
            bc_ts_activated = bc_event_data['timestamp_break_dt'] # Use the datetime object
            if bc_ts_activated is None or bc_ts_activated <= ob_ts_created: continue

            break_candle_idx = -1
            try: 
                # Ensure df_with_atr.index is timezone-aware for comparison
                if df_with_atr.index.tzinfo is None: df_with_atr.index = df_with_atr.index.tz_localize('UTC')
                
                if bc_ts_activated in df_with_atr.index: break_candle_idx = df_with_atr.index.get_loc(bc_ts_activated)
                else: 
                    nearest_idx_pos = df_with_atr.index.get_indexer([bc_ts_activated], method='nearest')
                    if nearest_idx_pos.size > 0 and nearest_idx_pos[0] != -1:
                        break_candle_idx = nearest_idx_pos[0]
                        time_diff_hours = abs((df_with_atr.index[break_candle_idx] - bc_ts_activated).total_seconds()) / 3600
                        if time_diff_hours > 12 : continue 
                    else: continue
            except Exception as e_idx: 
                logger.debug(f"Error finding index for break candle at {bc_ts_activated}: {e_idx}")
                continue

            if break_candle_idx == -1 or break_candle_idx >= len(df_with_atr): continue # Check bounds

            break_candle = df_with_atr.iloc[break_candle_idx]
            break_candle_close = break_candle[close_col]
            break_candle_body = abs(break_candle[close_col] - break_candle[open_col])
            atr_at_break_candle = df_with_atr['ATR_Value'].iloc[break_candle_idx]
            if not pd.notna(atr_at_break_candle) or atr_at_break_candle <= constants.ATR_MIN_VALUE: 
                atr_at_break_candle = mean_atr_fallback
            if atr_at_break_candle == 0: atr_at_break_candle = constants.ATR_MIN_VALUE

            breaker_type = None
            displacement_through_ob_score = 0.0

            if ob_data['type'] == 'bullish' and 'bearish' in bc_event_data.get('type','').lower():
                if break_candle_close < ob_bottom_price: 
                    breaker_type = 'bearish_breaker'
                    if ob_range > 0:
                        closed_beyond_distal = ob_bottom_price - break_candle_close
                        displacement_through_ob_score = (closed_beyond_distal / atr_at_break_candle) * displacement_through_ob_factor if closed_beyond_distal > 0 else 0
            elif ob_data['type'] == 'bearish' and 'bullish' in bc_event_data.get('type','').lower():
                if break_candle_close > ob_top_price: 
                    breaker_type = 'bullish_breaker'
                    if ob_range > 0:
                        closed_beyond_distal = break_candle_close - ob_top_price
                        displacement_through_ob_score = (closed_beyond_distal / atr_at_break_candle) * displacement_through_ob_factor if closed_beyond_distal > 0 else 0
            
            if breaker_type:
                current_bb_score = (bc_event_data.get('confirmation_quality_score', 0) * break_strength_factor_for_bb_score) + \
                                   (ob_data.get('strength_score', 0) * (1 - break_strength_factor_for_bb_score))
                current_bb_score += displacement_through_ob_score

                fvg_created_by_break_details = None
                if bc_event_data.get('fvg_created_on_break') and isinstance(bc_event_data.get('fvg_details'), dict):
                    fvg_bc = bc_event_data['fvg_details']
                    if (breaker_type == 'bullish_breaker' and fvg_bc.get('type') == 'bullish') or \
                       (breaker_type == 'bearish_breaker' and fvg_bc.get('type') == 'bearish'):
                        fvg_created_by_break_details = fvg_bc
                        current_bb_score += fvg_on_break_bonus_bb 

                internal_fvg_details = None
                for fvg_refined in fvgs:
                    if not isinstance(fvg_refined, dict): continue
                    fvg_start_price = fvg_refined.get('start_price'); fvg_end_price = fvg_refined.get('end_price')
                    if fvg_start_price is None or fvg_end_price is None: continue
                    fvg_min, fvg_max = min(fvg_start_price, fvg_end_price), max(fvg_start_price, fvg_end_price)
                    overlap_start = max(ob_bottom_price, fvg_min); overlap_end = min(ob_top_price, fvg_max)
                    if overlap_start < overlap_end: 
                        if (breaker_type == 'bullish_breaker' and fvg_refined.get('type') == 'bullish') or \
                           (breaker_type == 'bearish_breaker' and fvg_refined.get('type') == 'bearish'):
                            if fvg_refined.get('mitigation_info',{}).get('status', 'unmitigated') == 'unmitigated':
                                internal_fvg_details = fvg_refined.copy()
                                current_bb_score += internal_fvg_in_bb_bonus + (fvg_refined.get('importance_score', 0) * 0.1)
                                break
                
                bb_dict_initial = {
                    'top_price': round(ob_top_price, 5), 'bottom_price': round(ob_bottom_price, 5),
                    'mean_threshold': round((ob_top_price + ob_bottom_price) / 2, 5),
                    'timestamp_original_ob_created': ob_ts_created.isoformat(),
                    'timestamp_breaker_activated': bc_ts_activated.isoformat(),
                    'type': breaker_type,
                    'original_ob_details': {k: v for k, v in ob_data.items() if k != 'timestamp_created_dt'}, # Exclude temporary dt object
                    'activating_bos_choch_event_details': {k: v for k, v in bc_event_data.items() if k != 'timestamp_break_dt'},
                    'strength_score': round(min(max_bb_score, current_bb_score), 1),
                    'fvg_created_by_break_details': fvg_created_by_break_details.copy() if fvg_created_by_break_details else None,
                    'internal_fvg_details': internal_fvg_details.copy() if internal_fvg_details else None,
                    'atr_at_activation': round(atr_at_break_candle, 5),
                    'mitigation_status': "unretested", 
                    'retest_info': {} 
                }
                
                bb_with_retest_info = _check_bb_retest_and_outcome(
                    bb_dict_initial, df_with_atr, break_candle_idx, 
                    retest_lookahead_bb, close_col, high_col, low_col
                )
                breaker_blocks_details.append(bb_with_retest_info)
                break 

    logger.info(f"Detected {len(breaker_blocks_details)} Breaker Blocks (enhanced detection).")
    return sorted(breaker_blocks_details, key=lambda x: pd.to_datetime(x['timestamp_breaker_activated'], utc=True))

logger.info("ict_analyzer_package/bb_detector.py (enhanced version with corrected constant names) loaded.")
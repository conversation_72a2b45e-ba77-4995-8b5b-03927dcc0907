# ict_analyzer_package/fvg_detector.py
"""
شناسایی Fair Value Gaps (FVG) (نسخه بهبود یافته)
Detects Fair Value Gaps (FVGs) with enhanced contextual scoring and mitigation analysis.
"""
import pandas as pd
import logging
import numpy as np
from typing import List, Dict, Optional 

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def find_fvgs(
    df: pd.DataFrame,
    atr_period: int = constants.ATR_PERIOD_DEFAULT,
    atr_multiplier_min_size: float = constants.FVG_ATR_MULTIPLIER_MIN_SIZE_DEFAULT,
    middle_candle_strength_thresh: float = constants.FVG_MIDDLE_CANDLE_STRENGTH_THRESHOLD_DEFAULT,
    swings: Optional[List[Dict]] = None, 
    bos_choch_events: Optional[List[Dict]] = None, 
    max_mitigation_lookahead: int = constants.FVG_MAX_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT, 
    fvg_post_sweep_bonus: float = constants.FVG_POST_SWEEP_BONUS_DEFAULT, 
    fvg_from_swing_displacement_bonus: float = constants.FVG_FROM_SWING_DISPLACEMENT_BONUS_DEFAULT, 
    middle_candle_strength_score_factor: float = constants.FVG_MIDDLE_CANDLE_STRENGTH_SCORE_FACTOR_DEFAULT, 
    fvg_size_score_factor: float = constants.FVG_SIZE_SCORE_FACTOR_DEFAULT, 
    max_fvg_score: float = constants.MAX_FVG_IMPORTANCE_SCORE_DEFAULT, 
    fvg_at_bos_choch_bonus: float = constants.FVG_AT_BOS_CHOCH_BONUS_DEFAULT, 
    unmitigated_factor: float = constants.FVG_UNMITIGated_IMPORTANCE_FACTOR_DEFAULT, 
    partially_mitigated_low_fill_factor: float = constants.FVG_PARTIALLY_MITIGATED_LOW_FILL_FACTOR_DEFAULT, 
    partial_mitigation_low_fill_threshold_pct: float = constants.FVG_PARTIAL_MITIGATION_THRESHOLD_PCT_DEFAULT 
) -> List[Dict]:
    fvgs_details = []
    if df.empty or len(df) < 3: 
        logger.warning("Data insufficient for FVG detection (need at least 3 candles).")
        return fvgs_details

    df_with_atr = ensure_atr(df.copy(), period=atr_period) 
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all():
        logger.error("ATR_Value column missing or all NaN after ensure_atr. FVG detection may be unreliable.")
        avg_range = (df_with_atr['high'] - df_with_atr['low']).mean() if 'high' in df_with_atr.columns and 'low' in df_with_atr.columns else constants.ATR_MIN_VALUE * 10
        df_with_atr['ATR_Value'] = pd.Series(np.full(len(df_with_atr), max(constants.ATR_MIN_VALUE, avg_range * 0.01)), index=df_with_atr.index)

    swings = swings if swings is not None else [] 
    bos_choch_events = bos_choch_events if bos_choch_events is not None else [] 

    high_col = 'High' if 'High' in df_with_atr.columns else 'high' 
    low_col = 'Low' if 'Low' in df_with_atr.columns else 'low' 
    open_col = 'Open' if 'Open' in df_with_atr.columns else 'open' 
    close_col = 'Close' if 'Close' in df_with_atr.columns else 'close' 

    mean_atr_fallback = df_with_atr['ATR_Value'].mean() 
    if not pd.notna(mean_atr_fallback) or mean_atr_fallback <= constants.ATR_MIN_VALUE: 
        mean_atr_fallback = (df_with_atr[high_col] - df_with_atr[low_col]).mean() * 0.1 if not (df_with_atr[high_col] - df_with_atr[low_col]).empty else constants.ATR_MIN_VALUE * 10 
        if not pd.notna(mean_atr_fallback) or mean_atr_fallback <= constants.ATR_MIN_VALUE: 
            mean_atr_fallback = constants.ATR_MIN_VALUE * 10 

    # Determine a representative candle duration for lookbacks if freq is not set
    candle_duration_td = None
    if df_with_atr.index.freq:
        try:
            candle_duration_td = pd.tseries.frequencies.to_offset(df_with_atr.index.freq)
            if candle_duration_td is None: # If conversion results in None (e.g. for 'B')
                 if len(df_with_atr.index) >=2: candle_duration_td = pd.Series(df_with_atr.index).diff().median()
        except: # Fallback if freq string is unusual
             if len(df_with_atr.index) >=2: candle_duration_td = pd.Series(df_with_atr.index).diff().median()
    elif len(df_with_atr.index) >= 2:
        candle_duration_td = (pd.Series(df_with_atr.index).diff().dropna()).median()

    if candle_duration_td is None or pd.isna(candle_duration_td) or candle_duration_td.total_seconds() == 0:
        logger.warning("Could not determine reliable candle duration for FVG swing context. Using fixed 15min fallback.")
        candle_duration_td = pd.Timedelta(minutes=15) # Fallback to a common short timeframe duration

    lookback_timedelta_for_swing_context = 3 * candle_duration_td


    for i in range(len(df_with_atr) - 2): 
        c1, c2, c3 = df_with_atr.iloc[i], df_with_atr.iloc[i+1], df_with_atr.iloc[i+2] 
        ts_c1 = df_with_atr.index[i] # Keep as datetime for comparisons
        ts_c2 = df_with_atr.index[i+1]
        ts_c3 = df_with_atr.index[i+2]


        atr_val_c2 = df_with_atr['ATR_Value'].iloc[i+1] 
        if not pd.notna(atr_val_c2) or atr_val_c2 <= constants.ATR_MIN_VALUE: atr_val_c2 = mean_atr_fallback 

        min_fvg_size_abs = atr_val_c2 * atr_multiplier_min_size 
        fvg_type, fvg_start_price, fvg_end_price, fvg_specific_type = None, None, None, 'Standard_FVG' 
        base_importance_score = 1.0 
        contextual_bonus_score = 0.0

        if c1[low_col] > c3[high_col]: 
            fvg_type, fvg_start_price, fvg_end_price = 'bullish', c3[high_col], c1[low_col] 
            fvg_specific_type = 'SIBI' 
        elif c1[high_col] < c3[low_col]: 
            fvg_type, fvg_start_price, fvg_end_price = 'bearish', c1[high_col], c3[low_col] 
            fvg_specific_type = 'BISI' 

        if fvg_type:
            fvg_size_abs = abs(float(fvg_end_price) - float(fvg_start_price)) 
            c2_body_size = abs(c2[close_col] - c2[open_col]) 
            middle_candle_strength_vs_atr = (c2_body_size / atr_val_c2) if atr_val_c2 > 0 else 0 

            if fvg_size_abs >= min_fvg_size_abs: 
                base_importance_score += 1.0 
                base_importance_score += (fvg_size_abs / atr_val_c2) * fvg_size_score_factor

                if middle_candle_strength_vs_atr > middle_candle_strength_thresh: 
                    base_importance_score += middle_candle_strength_vs_atr * middle_candle_strength_score_factor 
                    fvg_specific_type += "_Strong_C2" 

                if any(isinstance(event, dict) and (pd.to_datetime(event.get('timestamp_break'), utc=True) == ts_c2 or
                       (pd.to_datetime(event.get('timestamp_break'), utc=True) == ts_c1 and event.get('fvg_created_on_break')))
                       for event in bos_choch_events):
                    contextual_bonus_score += fvg_at_bos_choch_bonus
                    fvg_specific_type += "_At_BOS_CHoCH"

                for swing in swings: # Check swing context
                    if not isinstance(swing, dict): continue
                    swing_ts_str = swing.get('timestamp')
                    if not swing_ts_str: continue
                    swing_ts = pd.to_datetime(swing_ts_str, utc=True)

                    # Check if swing occurred recently before or at C1 of FVG
                    if ts_c1 >= swing_ts > (ts_c1 - lookback_timedelta_for_swing_context): 
                        if swing.get('swept_liquidity_of_pivot_details') and \
                           ((fvg_type == 'bullish' and swing.get('type') == 'SL') or \
                            (fvg_type == 'bearish' and swing.get('type') == 'SH')):
                            contextual_bonus_score += fvg_post_sweep_bonus
                            fvg_specific_type += "_Post_Sweep"
                            break # Apply bonus once per FVG for sweep context
                
                for swing in swings: # Check displacement context
                    if not isinstance(swing, dict): continue
                    swing_ts_str = swing.get('timestamp')
                    if not swing_ts_str: continue
                    swing_ts = pd.to_datetime(swing_ts_str, utc=True)
                    if swing_ts == ts_c1: # If C1 is the swing point itself
                        if ((fvg_type == 'bullish' and swing.get('type') == 'SL') or \
                            (fvg_type == 'bearish' and swing.get('type') == 'SH')) and \
                           swing.get('reaction_strength_atr', 0) > constants.MIN_REACTION_FOR_STRUCTURAL_CANDIDATE_DEFAULT:
                            reaction_strength_ratio = swing.get('reaction_strength_atr',0) / constants.MIN_REACTION_FOR_STRUCTURAL_CANDIDATE_DEFAULT
                            contextual_bonus_score += fvg_from_swing_displacement_bonus * reaction_strength_ratio
                            fvg_specific_type += "_From_Swing_Displ"
                            break # Apply bonus once
                
                initial_score_before_mitigation = base_importance_score + contextual_bonus_score

                mitigation_details = {"status": "unmitigated", "mitigation_percentage": 0.0, "mitigated_by_ts": None, "mitigation_type": "none"}
                for k in range(i + 3, min(len(df_with_atr), i + 3 + max_mitigation_lookahead)): 
                    pk_candle = df_with_atr.iloc[k]; pk_ts_obj = df_with_atr.index[k]
                    mit_pct = 0.0; wick_fill = False; body_fill = False

                    if fvg_type == 'bullish':
                        if pk_candle[low_col] <= fvg_end_price: 
                            wick_fill = True; fill_depth = fvg_end_price - pk_candle[low_col]
                            mit_pct = min(1.0, fill_depth / fvg_size_abs if fvg_size_abs > 0 else 0) * 100
                            if pk_candle[close_col] <= fvg_end_price : body_fill = True
                            if pk_candle[low_col] <= fvg_start_price: mit_pct = 100.0
                    elif fvg_type == 'bearish':
                        if pk_candle[high_col] >= fvg_start_price: 
                            wick_fill = True; fill_depth = pk_candle[high_col] - fvg_start_price
                            mit_pct = min(1.0, fill_depth / fvg_size_abs if fvg_size_abs > 0 else 0) * 100
                            if pk_candle[close_col] >= fvg_start_price: body_fill = True
                            if pk_candle[high_col] >= fvg_end_price: mit_pct = 100.0
                    
                    if wick_fill:
                        mitigation_details['mitigation_percentage'] = round(max(mitigation_details.get('mitigation_percentage',0), mit_pct), 1)
                        if mitigation_details['mitigated_by_ts'] is None: mitigation_details['mitigated_by_ts'] = pk_ts_obj.isoformat()
                        
                        if mit_pct >= 99.9: 
                            mitigation_details['status'] = 'fully_mitigated'
                            mitigation_details['mitigation_type'] = "full_fill_by_wick_or_body" if body_fill else "full_fill_by_wick"
                            break 
                        else:
                            mitigation_details['status'] = 'partially_mitigated'
                            if body_fill and mitigation_details['mitigation_type'] != "body_close_into_fvg":
                                 mitigation_details['mitigation_type'] = "body_close_into_fvg"
                            elif mitigation_details['mitigation_type'] == "none": 
                                 mitigation_details['mitigation_type'] = "wick_fill"
                
                final_score = initial_score_before_mitigation
                if mitigation_details['status'] == 'unmitigated':
                    final_score *= unmitigated_factor 
                elif mitigation_details['status'] == 'partially_mitigated' and \
                     mitigation_details.get('mitigation_percentage', 101.0) < partial_mitigation_low_fill_threshold_pct: 
                    final_score *= partially_mitigated_low_fill_factor 
                    fvg_specific_type += f"_PartMit_LowFill{mitigation_details.get('mitigation_percentage',0):.0f}%" 

                fvgs_details.append({
                    'start_price': round(float(fvg_start_price), 5), 
                    'end_price': round(float(fvg_end_price), 5), 
                    'consequent_encroachment': round((float(fvg_start_price) + float(fvg_end_price)) / 2, 5), 
                    'timestamp_created': ts_c2.isoformat(), 
                    'type': fvg_type, 
                    'fvg_type_specific': fvg_specific_type, 
                    'mitigation_info': mitigation_details, 
                    'importance_score': round(min(max_fvg_score, final_score), 1), 
                    'initial_importance_score': round(min(max_fvg_score, initial_score_before_mitigation), 1), 
                    'middle_candle_strength_vs_atr': round(middle_candle_strength_vs_atr, 2), 
                    'size_vs_atr': round(fvg_size_abs / atr_val_c2, 2) if atr_val_c2 > 0 else 0, 
                    'size_absolute': round(fvg_size_abs, 5), 
                    'atr_at_creation': round(atr_val_c2, 5) 
                })

    logger.info(f"Detected {len(fvgs_details)} FVGs (enhanced detection with Timedelta fix).")
    return sorted(fvgs_details, key=lambda x: pd.to_datetime(x['timestamp_created'],utc=True)) 

logger.info("ict_analyzer_package/fvg_detector.py (enhanced version, constant name and Timedelta fix) loaded.")
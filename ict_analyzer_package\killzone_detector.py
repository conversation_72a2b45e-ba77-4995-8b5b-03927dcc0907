# ict_analyzer_package/killzone_detector.py
"""
شناسایی کیلزون‌های بازار و دوره‌های شانه ای آنها (نسخه بهبود یافته)
Detects market killzones and their shoulder periods (Enhanced version).
"""
from datetime import datetime, timezone, timedelta
import pytz # For timezone handling
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

def get_market_killzones( # Kept original name for easier integration in analyzer.py
    current_time_utc_iso: str,
    killzone_definitions: List[Dict] # Definitions now potentially include shoulder minutes
) -> List[Dict]:
    """
    شناسایی کیلزون‌های بازار فعال و دوره‌های شانه ای آنها (pre/post shoulders).
    Each definition in `killzone_definitions` can optionally have:
    "pre_shoulder_minutes": int
    "post_shoulder_minutes": int
    """
    active_periods = [] # Will store active main KZs and active shoulders

    try: #
        current_time_utc = datetime.fromisoformat(current_time_utc_iso.replace("Z", "+00:00"))
        current_time_utc = current_time_utc.astimezone(pytz.utc) if current_time_utc.tzinfo else pytz.utc.localize(current_time_utc)
    except Exception as e_time_parse: #
        logger.error(f"Error parsing current_time_utc_iso '{current_time_utc_iso}': {e_time_parse}")
        return active_periods

    if not killzone_definitions: #
        logger.info("No killzone definitions provided to get_market_killzones.") 
        return active_periods

    for kz_def_item in killzone_definitions: #
        try:
            required_keys = ["name", "start_hour_local", "end_hour_local", "tz_name", "type", "session_base"] #
            if not all(key in kz_def_item for key in required_keys): #
                logger.warning(f"Skipping KZ definition due to missing keys: {kz_def_item.get('name', 'Unnamed KZ')}")
                continue

            kz_base_tz = pytz.timezone(kz_def_item["tz_name"]) #
            current_time_in_kz_base_tz = current_time_utc.astimezone(kz_base_tz) #
            today_in_kz_tz = current_time_in_kz_base_tz.date()

            kz_start_hour_local = int(kz_def_item["start_hour_local"]) #
            kz_end_hour_local = int(kz_def_item["end_hour_local"]) #
            
            kz_start_time_naive = datetime.min.time().replace(hour=kz_start_hour_local, minute=0, second=0, microsecond=0) #
            kz_end_time_naive = datetime.min.time().replace(hour=kz_end_hour_local, minute=0, second=0, microsecond=0) #

            main_kz_start_date_local = today_in_kz_tz #
            main_kz_end_date_local = today_in_kz_tz #

            if kz_end_time_naive <= kz_start_time_naive:  # Overnight session
                if current_time_in_kz_base_tz.time() >= kz_start_time_naive: 
                    main_kz_end_date_local = today_in_kz_tz + timedelta(days=1)
                else: 
                    main_kz_start_date_local = today_in_kz_tz - timedelta(days=1)
            
            main_kz_start_dt_local_candidate = datetime.combine(main_kz_start_date_local, kz_start_time_naive) #
            main_kz_end_dt_local_candidate = datetime.combine(main_kz_end_date_local, kz_end_time_naive) #
            
            main_kz_start_aware = kz_base_tz.localize(main_kz_start_dt_local_candidate, is_dst=None) #
            main_kz_end_aware = kz_base_tz.localize(main_kz_end_dt_local_candidate, is_dst=None) #

            # Check Pre-Shoulder
            pre_shoulder_minutes = kz_def_item.get("pre_shoulder_minutes") #
            if pre_shoulder_minutes is not None and isinstance(pre_shoulder_minutes, int) and pre_shoulder_minutes > 0: #
                pre_shoulder_end_aware = main_kz_start_aware #
                pre_shoulder_start_aware = main_kz_start_aware - timedelta(minutes=pre_shoulder_minutes) #
                if pre_shoulder_start_aware <= current_time_in_kz_base_tz < pre_shoulder_end_aware: #
                    time_rem_val = pre_shoulder_end_aware - current_time_in_kz_base_tz #
                    hr, rem_s = divmod(time_rem_val.total_seconds(), 3600); mr, _ = divmod(rem_s, 60) #
                    active_periods.append({ #
                        'killzone_name': kz_def_item['name'], 'period_type': "pre_shoulder",
                        'period_start_utc': pre_shoulder_start_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                        'period_end_utc': pre_shoulder_end_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                        'time_remaining_in_period': f"{int(hr)}h {int(mr)}m",
                        'original_kz_type': kz_def_item['type'], 'original_kz_session_base': kz_def_item.get('session_base', 'N/A')
                    })
            
            # Check Main Killzone
            if main_kz_start_aware <= current_time_in_kz_base_tz < main_kz_end_aware: #
                time_rem_val = main_kz_end_aware - current_time_in_kz_base_tz #
                hr, rem_s = divmod(time_rem_val.total_seconds(), 3600); mr, _ = divmod(rem_s, 60) #
                active_periods.append({ #
                    'killzone_name': kz_def_item['name'], 'period_type': "main_killzone",
                    'period_start_utc': main_kz_start_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                    'period_end_utc': main_kz_end_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                    'time_remaining_in_period': f"{int(hr)}h {int(mr)}m",
                    'original_kz_type': kz_def_item['type'], 'original_kz_session_base': kz_def_item.get('session_base', 'N/A')
                })

            # Check Post-Shoulder
            post_shoulder_minutes = kz_def_item.get("post_shoulder_minutes") #
            if post_shoulder_minutes is not None and isinstance(post_shoulder_minutes, int) and post_shoulder_minutes > 0: #
                post_shoulder_start_aware = main_kz_end_aware #
                post_shoulder_end_aware = main_kz_end_aware + timedelta(minutes=post_shoulder_minutes) #
                if post_shoulder_start_aware <= current_time_in_kz_base_tz < post_shoulder_end_aware: #
                    time_rem_val = post_shoulder_end_aware - current_time_in_kz_base_tz #
                    hr, rem_s = divmod(time_rem_val.total_seconds(), 3600); mr, _ = divmod(rem_s, 60) #
                    active_periods.append({ #
                        'killzone_name': kz_def_item['name'], 'period_type': "post_shoulder",
                        'period_start_utc': post_shoulder_start_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                        'period_end_utc': post_shoulder_end_aware.astimezone(pytz.utc).strftime('%Y-%m-%d %H:%M UTC'),
                        'time_remaining_in_period': f"{int(hr)}h {int(mr)}m",
                        'original_kz_type': kz_def_item['type'], 'original_kz_session_base': kz_def_item.get('session_base', 'N/A')
                    })

        except pytz.exceptions.AmbiguousTimeError: #
            logger.warning(f"Ambiguous time for KZ {kz_def_item.get('name','Unnamed KZ')}. Skipping.")
            continue
        except pytz.exceptions.NonExistentTimeError: #
            logger.warning(f"Non-existent time for KZ {kz_def_item.get('name','Unnamed KZ')}. Skipping.")
            continue
        except ValueError as ve: #
            logger.error(f"ValueError for KZ '{kz_def_item.get('name','Unnamed KZ')}': {ve}. Check hours (0-23).")
            continue
        except Exception as e_kz_item: #
            logger.error(f"Error processing KZ definition '{kz_def_item.get('name','Unnamed KZ')}': {e_kz_item}", exc_info=False)
            continue

    logger.info(f"Identified {len(active_periods)} active killzone or shoulder periods.") #
    active_periods.sort(key=lambda x: x['period_start_utc']) #
    return active_periods

logger.info("ict_analyzer_package/killzone_detector.py (with shoulder period logic) loaded.")
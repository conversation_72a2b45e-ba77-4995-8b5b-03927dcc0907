# ict_analyzer_package/__init__.py
"""
پکیج تحلیلگر ICT
ICT Analyzer Package.
"""
import logging

# Import the main analysis function to make it easily accessible
from .analyzer import analyze_ict_concepts_v4

# Import individual detector functions if they are intended to be part of the public API of the package
from .trend_detector import determine_market_trend
from .swing_detector import detect_swings
from .fvg_detector import find_fvgs
from .structure_detector import find_bos_choch
from .ob_detector import find_order_blocks
from .bb_detector import find_breaker_blocks
from .liquidity_detector import find_key_liquidity_pools
from .killzone_detector import get_market_killzones
from .ote_detector import find_ote_levels
from .pattern_detector import detect_major_sweep_and_shift_patterns # New pattern detector

# Import constants to make them accessible via the package, e.g., ict_analyzer_package.ATR_PERIOD_DEFAULT
from .constants import (
    ATR_PERIOD_DEFAULT,
    SHORT_EMA_PERIOD,
    MID_EMA_PERIOD,
    LONG_EMA_PERIOD,
    DEFAULT_SWING_WINDOW,
    # Add other key constants you might want to access directly via the package path
    TREND_DETECTOR_CFG_KEYS,
    SWING_DETECTOR_CFG_KEYS,
    FVG_DETECTOR_CFG_KEYS,
    STRUCTURE_DETECTOR_CFG_KEYS,
    OB_DETECTOR_CFG_KEYS,
    BB_DETECTOR_CFG_KEYS,
    LIQ_DETECTOR_CFG_KEYS,
    OTE_DETECTOR_CFG_KEYS,
    SWEEP_SHIFT_DETECTOR_CFG_KEYS
)

# Configure a logger for the package if not already configured by the main application
# This helps if the package modules are used independently.
logger = logging.getLogger(__name__)

# Optionally, set a default log level for the package if no handlers are configured
if not logger.handlers:
    # Only add a handler if none exist to avoid duplicate logs
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

logger.info("ict_analyzer_package (v_final_all_enhancements) initialized.")

__all__ = [
    'analyze_ict_concepts_v4',
    'determine_market_trend',
    'detect_swings',
    'find_fvgs',
    'find_bos_choch',
    'find_order_blocks',
    'find_breaker_blocks',
    'find_key_liquidity_pools',
    'get_market_killzones',
    'find_ote_levels',
    'detect_major_sweep_and_shift_patterns', # Export new function
    'ATR_PERIOD_DEFAULT', # Example of exporting a constant
    'TREND_DETECTOR_CFG_KEYS', 
    'SWING_DETECTOR_CFG_KEYS',
    'FVG_DETECTOR_CFG_KEYS',
    'STRUCTURE_DETECTOR_CFG_KEYS',
    'OB_DETECTOR_CFG_KEYS',
    'BB_DETECTOR_CFG_KEYS',
    'LIQ_DETECTOR_CFG_KEYS',
    'OTE_DETECTOR_CFG_KEYS',
    'SWEEP_SHIFT_DETECTOR_CFG_KEYS',
]

# main.py
"""
نقطه ورود اصلی برنامه تحلیل تکنیکال ICT با هوش مصنوعی.
شامل منوی کاربری برای تحلیل تک نمادی، اسکن خودکار، و پیگیری ستاپ‌ها.
"""

# --- Load API Keys First ---
try:
    import key_token  # This will set the environment variables
    print("API keys loaded from key-token.py")
except ImportError:
    print("Warning: key-token.py not found. API keys must be set manually.")

import asyncio
import logging
import os
import sys
import time
from datetime import datetime, timezone
import pytz
import jdatetime
from typing import Optional, Dict, Any, List # Ensure List is imported if used

# --- تنظیمات اولیه Logging ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
root_logger_main = logging.getLogger()
if root_logger_main.hasHandlers(): root_logger_main.handlers.clear()
root_logger_main.setLevel(logging.INFO)
stream_handler_main = logging.StreamHandler(sys.stdout)
stream_handler_main.setFormatter(log_formatter)
root_logger_main.addHandler(stream_handler_main)
logger = logging.getLogger(__name__)
logger.info("Logging setup for main.py applied.")

# --- Path Setup ---
gdrive_available_flag_main = False
# This is the explicit path to your project root in Google Drive
# Ensure this path is EXACTLY correct.
PROJECT_ROOT_DRIVE_PATH = "/content/drive/MyDrive/TechnicalAnalysisAgentData_V3_Refactored"

output_dir_to_use: str
prev_analysis_dir_to_use: str
project_base_path_to_use: str

if 'google.colab' in sys.modules and os.path.isdir(PROJECT_ROOT_DRIVE_PATH):
    # This block should ideally be run in a Colab cell *before* !python main.py
    # to ensure Drive is mounted for the !python environment.
    # However, main.py will try to add the path assuming Drive is already mounted.
    project_base_path_to_use = PROJECT_ROOT_DRIVE_PATH
    gdrive_available_flag_main = True
    logger.info(f"Colab environment detected. Assuming Drive mounted. Project base set to: {project_base_path_to_use}")
elif os.path.isdir(PROJECT_ROOT_DRIVE_PATH): # If path exists (e.g. Drive manually mounted, or local copy)
    project_base_path_to_use = PROJECT_ROOT_DRIVE_PATH
    gdrive_available_flag_main = True # It's available if the path is there
    logger.info(f"Project path {PROJECT_ROOT_DRIVE_PATH} found. Using it as project base.")
else: # Fallback for truly local execution where main.py is at project root
    logger.info("Running in a non-Colab or non-Drive-mounted environment. Assuming main.py is in project root.")
    project_base_path_to_use = os.getcwd()
    gdrive_available_flag_main = False # Explicitly false if not using the Drive path

# Add project base path to sys.path for module imports
if project_base_path_to_use not in sys.path:
    sys.path.insert(0, project_base_path_to_use) # Insert at beginning to prioritize project modules
    logger.info(f"Path '{project_base_path_to_use}' added to sys.path.")

# Define output and previous analysis directories based on the determined base path
output_dir_relative = "output_files_ict"
prev_analysis_dir_relative = "previous_analysis_data_ict"

output_dir_to_use = os.path.join(project_base_path_to_use, output_dir_relative + ("" if gdrive_available_flag_main and PROJECT_ROOT_DRIVE_PATH in project_base_path_to_use else "_local"))
prev_analysis_dir_to_use = os.path.join(project_base_path_to_use, prev_analysis_dir_relative + ("" if gdrive_available_flag_main and PROJECT_ROOT_DRIVE_PATH in project_base_path_to_use else "_local"))

os.makedirs(output_dir_to_use, exist_ok=True)
os.makedirs(prev_analysis_dir_to_use, exist_ok=True)
logger.info(f"Output directory set to: {output_dir_to_use}")
logger.info(f"Previous analysis directory set to: {prev_analysis_dir_to_use}")


# --- وارد کردن ماژول‌های اصلی برنامه ---
# These imports should happen AFTER sys.path is potentially modified.
try:
    import config as config_module_ref
    from chart_agent_module import ChartAgent
    from setup_tracker import (
        get_open_setup_for_symbol,
        track_and_report_all_active_setups, # CORRECTED
        track_specific_open_setup,      # CORRECTED
        add_new_setup
    )
    import nest_asyncio
except ImportError as e_import_main:
    logger.critical(f"Critical error importing main application modules: {e_import_main}", exc_info=True)
    logger.critical(f"Sys.path: {sys.path}")
    logger.critical("Ensure you are in the correct project path and all required files exist in Google Drive and Drive is mounted.")
    sys.exit(f"Import Error: {e_import_main} - Cannot start application.")

# به روز رسانی مسیرهای داینامیک در ماژول config
if config_module_ref:
    config_module_ref.update_dynamic_paths(output_dir_to_use, prev_analysis_dir_to_use, project_base_path_to_use, gdrive_available_flag_main)
else:
    logger.error("config_module_ref is not available to update dynamic paths.") # Should not happen if import was successful


def check_python_version(): #
    if sys.version_info < (3, 9):
        logger.warning("Your Python version is less than 3.9. Some features might not work correctly.")
        return False
    logger.info(f"Python version {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} is sufficient.")
    return True

def check_and_load_libraries(): #
    logger.info("Checking and loading necessary libraries...")
    libraries_ok = True
    try: import openai; logger.info("OpenAI library loaded.")
    except ImportError: logger.warning("OpenAI library not found. AI analysis will not be available."); libraries_ok = False
    try: import mplfinance; logger.info("mplfinance library loaded.")
    except ImportError: logger.warning("mplfinance library not found. Chart plotting will be disabled."); libraries_ok = False
    try: from docx import Document; logger.info("python-docx library loaded.")
    except ImportError: logger.warning("python-docx library not found. DOCX export will be disabled."); libraries_ok = False
    try: from fpdf import FPDF; logger.info("fpdf2 library loaded.")
    except ImportError: logger.warning("fpdf2 library not found. PDF export will be disabled."); libraries_ok = False
    try: import arabic_reshaper; from bidi.algorithm import get_display; logger.info("arabic_reshaper and python-bidi loaded.")
    except ImportError: logger.warning("arabic_reshaper or python-bidi not found. Persian text in PDF/DOCX might not render correctly.")

    if config_module_ref: config_module_ref.update_libraries_loaded_status(libraries_ok)
    else: logger.error("config_module_ref not available in check_and_load_libraries.")


async def main_program_loop(): #
    agent = ChartAgent()
    while True:
        print("\n--- منوی اصلی تحلیلگر ICT ---")
        print("1. تحلیل یک جفت ارز خاص")
        print("2. شروع اسکن خودکار لیست ارزها")
        print("3. پیگیری تمامی ستاپ‌های باز")
        print("4. پیگیری یک ستاپ خاص با نماد")
        print("5. خروج")
        choice = input("گزینه مورد نظر را وارد کنید (۱-۵): ")

        if choice == '1':
            fsym_input = input("نماد اصلی (مثلاً BTC): ").upper()
            tsym_input = input("نماد مقابل (مثلاً USD): ").upper()
            if fsym_input and tsym_input:
                agent.set_currency_pair(fsym_input, tsym_input)
                await agent.run(source_data_mode='C', is_auto_scan_run=False)
            else: print("نمادها به درستی وارد نشدند.")
        elif choice == '2':
            logger.info("شروع اسکن خودکار...")
            print("\n--- شروع اسکن خودکار لیست ارزها ---")
            for pair_info in config_module_ref.AUTO_SCAN_CURRENCY_PAIRS:
                fsym_auto, tsym_auto = pair_info.get("fsym"), pair_info.get("tsym")
                if fsym_auto and tsym_auto:
                    agent.set_currency_pair(fsym_auto, tsym_auto)
                    print(f"\n--- در حال تحلیل {config_module_ref.TICKER_DISPLAY_NAME_GLOBAL} (اسکن خودکار) ---")
                    await agent.run(source_data_mode='C', is_auto_scan_run=True)
                    if len(config_module_ref.AUTO_SCAN_CURRENCY_PAIRS) > 1 :
                         logger.info(f"تاخیر {config_module_ref.DELAY_BETWEEN_AUTO_SCAN_PAIRS_SECONDS} ثانیه قبل از ارز بعدی...")
                         await asyncio.sleep(config_module_ref.DELAY_BETWEEN_AUTO_SCAN_PAIRS_SECONDS)
                else: logger.warning(f"جفت ارز نامعتبر در لیست اسکن خودکار: {pair_info}")
            logger.info("اسکن خودکار تمام شد.")
            print("\n--- اسکن خودکار لیست ارزها به پایان رسید ---")
        elif choice == '3':
            print("\n--- در حال پیگیری تمامی ستاپ‌های باز... ---")
            await track_and_report_all_active_setups(send_to_telegram_flag=True)
        elif choice == '4':
            fsym_input_track = input("نماد اصلی ستاپ برای پیگیری (مثلاً BTC): ").upper()
            tsym_input_track = input("نماد مقابل ستاپ (مثلاً USD): ").upper()
            if fsym_input_track and tsym_input_track:
                print(f"\n--- در حال پیگیری ستاپ باز برای {fsym_input_track}-{tsym_input_track}... ---")
                await track_specific_open_setup(fsym_input_track, tsym_input_track, send_to_telegram_flag=True)
            else: print("نمادها برای پیگیری به درستی وارد نشدند.")
        elif choice == '5':
            print("خروج از برنامه...")
            break
        else:
            print("گزینه نامعتبر. لطفاً عددی بین ۱ تا ۵ وارد کنید.")

if __name__ == "__main__":
    check_python_version()
    if 'nest_asyncio' in sys.modules: # Check if already imported
        nest_asyncio.apply()
    else:
        try:
            import nest_asyncio
            nest_asyncio.apply()
            logger.info("nest_asyncio applied.")
        except ImportError:
            logger.warning("nest_asyncio not found. Async operations in Jupyter/Colab might have issues.")

    check_and_load_libraries()

    critical_error_occurred = False
    if not config_module_ref:
        logger.critical("config module failed to load. Cannot continue.")
        critical_error_occurred = True
    elif not config_module_ref.LIBRARIES_LOADED_SUCCESSFULLY and not gdrive_available_flag_main :
        logger.warning("Some optional libraries not loaded. Certain features might be disabled.")

    if not critical_error_occurred and config_module_ref:
        if not config_module_ref.CRYPTOCOMPARE_API_KEY:
            logger.critical("CryptoCompare API Key not set. Cannot fetch price data. Exiting.")
            critical_error_occurred = True
        if not config_module_ref.AVALAI_API_KEY:
            logger.warning("AvalAI API Key not set. AI analysis will be skipped.")

    if not critical_error_occurred:
        try:
            asyncio.run(main_program_loop())
        except RuntimeError as e_runtime_main_exec:
            if "cannot be called when an event loop is running" in str(e_runtime_main_exec).lower() or \
               "this event loop is already running" in str(e_runtime_main_exec).lower():
                logger.error(f"Runtime error related to event loop: {e_runtime_main_exec}.")
                logger.error("If in a notebook, try `await main_program_loop()` in a new cell if nest_asyncio failed.")
            else: logger.exception(f"Unexpected runtime error: {e_runtime_main_exec}")
        except KeyboardInterrupt: logger.info("Operation stopped by user.")
        except Exception as e_global_unhandled_exec: logger.exception(f"Unexpected global error: {e_global_unhandled_exec}")
    else:
        logger.critical("Application halted due to critical errors (e.g., missing API keys).")

    logger.info("--- main.py execution finished ---")
#!/usr/bin/env python3
"""
Quick Start Launcher for ICT Analyzer
Double-click this file to run the main program
"""

import os
import sys
import subprocess

def main():
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Change to the project directory
    os.chdir(script_dir)
    
    print("🚀 Starting ICT Analyzer...")
    print(f"📁 Working directory: {script_dir}")
    print("-" * 50)
    
    try:
        # Run the main program
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️  Program stopped by user")
    except Exception as e:
        print(f"❌ Error running program: {e}")
        input("Press Enter to close...")

if __name__ == "__main__":
    main()

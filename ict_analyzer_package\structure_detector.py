# ict_analyzer_package/structure_detector.py
"""
شناسایی شکست ساختار (BOS) و تغییر وضعیت (CHoCH) (نسخه بهبود یافته)
Detects Break of Structure (BOS) and Change of Character (CHoCH) (Enhanced Version).
"""
import pandas as pd
import logging
from typing import Tuple, List, Dict, Optional 

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

# --- Helper Functions (from response #21) ---
def _calculate_displacement_score(breaking_candle: pd.Series,
                                  broken_level: float,
                                  atr_at_break: float,
                                  is_bullish_break: bool,
                                  close_col: str, open_col: str,
                                  cfg: dict) -> float: #
    if atr_at_break == 0: return 0.0
    candle_body_val = abs(breaking_candle[close_col] - breaking_candle[open_col])
    body_vs_atr_val = candle_body_val / atr_at_break 
    if is_bullish_break: dist_close_vs_level = (breaking_candle[close_col] - broken_level) / atr_at_break
    else: dist_close_vs_level = (broken_level - breaking_candle[close_col]) / atr_at_break
    dist_close_vs_level = max(0, dist_close_vs_level)
    score = (dist_close_vs_level * cfg.get("bos_choch_close_distance_weight", constants.BOS_CHOCH_CANDLE_CLOSE_DISTANCE_WEIGHT_DEFAULT)) + \
            (body_vs_atr_val * cfg.get("bos_choch_candle_body_weight", constants.BOS_CHOCH_CANDLE_BODY_WEIGHT_DEFAULT))
    return score

def _get_fvg_on_break_details(breaking_candle_ts: pd.Timestamp,
                              break_candle_index_in_df: int,
                              df_with_atr: pd.DataFrame,
                              initial_fvg_list: list, # This is the refined_fvg_list from analyzer for FVG quality
                              is_bullish_break: bool,
                              cfg: dict) -> Tuple[Optional[Dict], float]: #
    fvg_details_on_break = None; fvg_bonus = 0.0
    if break_candle_index_in_df + 1 < len(df_with_atr):
        expected_fvg_c2_ts = df_with_atr.index[break_candle_index_in_df + 1]
        for fvg in initial_fvg_list: # Should be refined_fvg_list with scores
            if not isinstance(fvg, dict): continue
            fvg_ts_created = pd.to_datetime(fvg.get('timestamp_created'), utc=True)
            if fvg_ts_created == expected_fvg_c2_ts:
                fvg_type = fvg.get('type', '')
                if (is_bullish_break and fvg_type == 'bullish') or (not is_bullish_break and fvg_type == 'bearish'):
                    fvg_details_on_break = fvg.copy()
                    fvg_quality_metric = fvg.get('importance_score',0) # Use final importance_score
                    fvg_bonus = fvg_quality_metric * cfg.get("fvg_on_break_score_multiplier", constants.FVG_ON_BREAK_SCORE_MULTIPLIER_DEFAULT)
                    break 
    return fvg_details_on_break, fvg_bonus

def _check_follow_through(df_with_atr: pd.DataFrame,
                          break_candle_index: int,
                          broken_level: float,
                          is_bullish_break: bool,
                          close_col: str, high_col: str, low_col: str,
                          cfg: dict) -> Tuple[bool, float]: #
    num_follow_through_candles = cfg.get("bos_choch_follow_through_candles", constants.BOS_CHOCH_FOLLOW_THROUGH_CANDLES_DEFAULT)
    atr_extension_needed = cfg.get("bos_choch_follow_through_atr_extension", constants.BOS_CHOCH_FOLLOW_THROUGH_ATR_EXTENSION_DEFAULT)
    follow_through_bonus = cfg.get("bos_choch_follow_through_score_bonus", constants.BOS_CHOCH_FOLLOW_THROUGH_SCORE_BONUS_DEFAULT)
    if num_follow_through_candles == 0: return False, 0.0
    followed_through = False
    current_atr_at_break_candle = df_with_atr['ATR_Value'].iloc[break_candle_index]
    if not pd.notna(current_atr_at_break_candle) or current_atr_at_break_candle == 0: current_atr_at_break_candle = constants.ATR_MIN_VALUE
    for k in range(1, num_follow_through_candles + 1):
        if break_candle_index + k >= len(df_with_atr): break
        follow_candle = df_with_atr.iloc[break_candle_index + k]
        if is_bullish_break:
            if follow_candle[close_col] > broken_level or follow_candle[high_col] > broken_level + (current_atr_at_break_candle * atr_extension_needed):
                followed_through = True; break
        else: 
            if follow_candle[close_col] < broken_level or follow_candle[low_col] < broken_level - (current_atr_at_break_candle * atr_extension_needed):
                followed_through = True; break
    return followed_through, follow_through_bonus if followed_through else 0.0

def _determine_event_type_and_update_bias(
    broken_swing: dict, is_bullish_break: bool, current_market_bias: str, cfg: dict
) -> Tuple[str, str, bool]: # event_type, new_market_bias, is_choch_flag
    event_type = "Unknown_Break"; new_market_bias = current_market_bias; is_choch_flag = False
    broken_swing_is_confirmed_structural = broken_swing.get('is_structural_confirmed', False)
    broken_swing_is_candidate_structural = broken_swing.get('is_structural_candidate', False)
    # Simplify trend bias from enhanced trend_detector output
    is_trending_bullish_bias = "bullish" in current_market_bias and "bearish" not in current_market_bias and "ranging" not in current_market_bias and "uncertain" not in current_market_bias
    is_trending_bearish_bias = "bearish" in current_market_bias and "bullish" not in current_market_bias and "ranging" not in current_market_bias and "uncertain" not in current_market_bias
    is_ranging_bias = "ranging" in current_market_bias or "uncertain" in current_market_bias or "conflicted" in current_market_bias or "flat" in current_market_bias # Broader check for ranging

    if is_bullish_break: # Breaking a Swing High
        if is_trending_bullish_bias: event_type = "BOS_bullish"
        elif is_trending_bearish_bias:
            if broken_swing_is_confirmed_structural: event_type = "Major_CHoCH_bullish"; is_choch_flag = True; new_market_bias = "bullish_bias_after_major_choch"
            elif broken_swing_is_candidate_structural: event_type = "Minor_CHoCH_bullish"; is_choch_flag = True; new_market_bias = "bullish_bias_after_minor_choch"
            else: event_type = "Weak_High_Broken_vs_BearBias"
        elif is_ranging_bias: event_type = "Range_Expansion_High"; new_market_bias = "bullish_bias_after_range_expansion"
        else: event_type = "BOS_bullish_Unclear_Bias"
    else: # Bearish break (Breaking a Swing Low)
        if is_trending_bearish_bias: event_type = "BOS_bearish"
        elif is_trending_bullish_bias:
            if broken_swing_is_confirmed_structural: event_type = "Major_CHoCH_bearish"; is_choch_flag = True; new_market_bias = "bearish_bias_after_major_choch"
            elif broken_swing_is_candidate_structural: event_type = "Minor_CHoCH_bearish"; is_choch_flag = True; new_market_bias = "bearish_bias_after_minor_choch"
            else: event_type = "Weak_Low_Broken_vs_BullBias"
        elif is_ranging_bias: event_type = "Range_Expansion_Low"; new_market_bias = "bearish_bias_after_range_expansion"
        else: event_type = "BOS_bearish_Unclear_Bias"
    return event_type, new_market_bias, is_choch_flag

def _find_and_confirm_origin_swing(
    broken_swing_ts: pd.Timestamp, is_bullish_break: bool, 
    all_swings_map: dict, sorted_swing_timestamps: list, cfg: dict
) -> Optional[Dict]: #
    origin_candidates = []; target_origin_swing_type = 'SL' if is_bullish_break else 'SH'
    start_index_for_search = -1
    try: start_index_for_search = sorted_swing_timestamps.index(broken_swing_ts) - 1
    except ValueError: logger.warning(f"Broken swing TS {broken_swing_ts} not in sorted list for origin search."); return None
    
    # Look back a configurable number of swings or a time period for origin
    lookback_swings_for_origin = cfg.get("structure_origin_swing_lookback_count", constants.STRUCTURE_ORIGIN_SWING_LOOKBACK_COUNT_DEFAULT)

    for k in range(start_index_for_search, max(-1, start_index_for_search - lookback_swings_for_origin), -1):
        candidate_ts = sorted_swing_timestamps[k]; candidate_swing = all_swings_map.get(candidate_ts)
        if not candidate_swing: continue
        if candidate_swing.get('type') == target_origin_swing_type and candidate_swing.get('is_structural_candidate'):
            is_valid_leg_origin = True
            for check_idx in range(k + 1, start_index_for_search + 1):
                intermediate_swing = all_swings_map.get(sorted_swing_timestamps[check_idx])
                if not intermediate_swing: continue
                if target_origin_swing_type == 'SL' and intermediate_swing.get('type') == 'SL' and intermediate_swing.get('price', float('inf')) < candidate_swing.get('price', float('-inf')): is_valid_leg_origin = False; break
                elif target_origin_swing_type == 'SH' and intermediate_swing.get('type') == 'SH' and intermediate_swing.get('price', float('-inf')) > candidate_swing.get('price', float('inf')): is_valid_leg_origin = False; break
            if is_valid_leg_origin:
                age_in_candles = start_index_for_search - k # Approx age in terms of number of swings prior
                score = (candidate_swing.get('reaction_strength_atr', 0) * cfg.get("origin_swing_reaction_weight", constants.ORIGIN_SWING_REACTION_WEIGHT_DEFAULT)) + \
                        (candidate_swing.get('combined_strength_score', 0) * cfg.get("origin_swing_combined_strength_weight", constants.ORIGIN_SWING_COMBINED_STRENGTH_WEIGHT_DEFAULT)) - \
                        (age_in_candles * cfg.get("origin_swing_age_penalty_factor", constants.ORIGIN_SWING_AGE_PENALTY_FACTOR_DEFAULT)) # Age penalty based on candles/swings
                origin_candidates.append({'swing': candidate_swing, 'score': score})
        if candidate_swing.get('is_structural_confirmed') and candidate_swing.get('type') == target_origin_swing_type: break 
    if not origin_candidates: return None
    return max(origin_candidates, key=lambda x: x['score'])['swing']


# --- Main Function ---
def find_bos_choch(
    df: pd.DataFrame, swings: list, trend_info: dict,
    atr_period: int = constants.ATR_PERIOD_DEFAULT,
    fvgs_list: Optional[List[Dict]] = None, 
    **kwargs # Captures all other config parameters from cfg
) -> Tuple[List[Dict], List[Dict]]: #
    structure_events = []; cfg = kwargs
    if df.empty or not swings or len(swings) < 2: logger.warning("Data/swings insufficient for BOS/CHoCH."); return structure_events, swings

    df_with_atr = ensure_atr(df.copy(), period=atr_period)
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all():
        df_with_atr['ATR_Value'] = constants.ATR_MIN_VALUE * 10
    
    close_col, open_col = ('Close' if 'Close' in df_with_atr.columns else 'close'), ('Open' if 'Open' in df_with_atr.columns else 'open')
    high_col, low_col = ('High' if 'High' in df_with_atr.columns else 'high'), ('Low' if 'Low' in df_with_atr.columns else 'low')

    processed_swings_map = {pd.to_datetime(s['timestamp'], utc=True): s.copy() for s in swings if isinstance(s,dict) and 'timestamp' in s}
    for ts_key in processed_swings_map:
        processed_swings_map[ts_key].setdefault('is_structural_confirmed', False)
        processed_swings_map[ts_key].setdefault('role_in_structure', 'Internal')
        processed_swings_map[ts_key]['broken_by_event_ts'] = None

    sorted_swing_timestamps = sorted(list(processed_swings_map.keys()))
    dynamic_market_bias = trend_info.get('overall_trend_consensus', "ranging")
    initial_fvgs = fvgs_list if fvgs_list is not None else []

    min_break_candles = 2 + cfg.get("bos_choch_follow_through_candles", constants.BOS_CHOCH_FOLLOW_THROUGH_CANDLES_DEFAULT)
    if len(df_with_atr) < min_break_candles :
        logger.warning(f"Not enough df candles ({len(df_with_atr)}) for break & follow-through ({min_break_candles}).")
        return structure_events, list(processed_swings_map.values())

    for i in range(1, len(df_with_atr)):
        current_candle = df_with_atr.iloc[i]; current_candle_ts = df_with_atr.index[i]
        current_atr_val = df_with_atr['ATR_Value'].iloc[i]
        if not pd.notna(current_atr_val) or current_atr_val <= constants.ATR_MIN_VALUE: current_atr_val = constants.ATR_MIN_VALUE * 10

        for swing_ts_key in sorted_swing_timestamps:
            if swing_ts_key >= current_candle_ts: continue
            swing_to_check = processed_swings_map[swing_ts_key]
            if swing_to_check.get('broken_by_event_ts') and pd.to_datetime(swing_to_check['broken_by_event_ts'],utc=True) < current_candle_ts: continue

            broken_pivot_price = swing_to_check['price']
            is_bullish_break_attempt = swing_to_check['type'] == 'SH'
            
            classic_break, gap_break_strong_close = False, False
            if is_bullish_break_attempt and current_candle[close_col] > broken_pivot_price:
                if current_candle[open_col] <= broken_pivot_price: classic_break = True
                elif current_candle[open_col] > broken_pivot_price : gap_break_strong_close = True # Gap up and continued
            elif not is_bullish_break_attempt and current_candle[close_col] < broken_pivot_price:
                if current_candle[open_col] >= broken_pivot_price: classic_break = True
                elif current_candle[open_col] < broken_pivot_price : gap_break_strong_close = True # Gap down and continued
            
            actual_break_this_candle = classic_break or gap_break_strong_close
            break_type_detail = "classic_close_through" if classic_break else ("gap_and_go_close" if gap_break_strong_close else "no_break")

            if actual_break_this_candle:
                displacement_score = _calculate_displacement_score(current_candle, broken_pivot_price, current_atr_val, is_bullish_break_attempt, close_col, open_col, cfg)
                if displacement_score >= cfg.get("bos_choch_displacement_threshold_min", constants.BOS_CHOCH_DISPLACEMENT_STRENGTH_THRESHOLD_MIN_DEFAULT):
                    fvg_on_break_obj, fvg_bonus = _get_fvg_on_break_details(current_candle_ts, i, df_with_atr, initial_fvgs, is_bullish_break_attempt, cfg)
                    follow_through_occurred, follow_through_score_bonus = _check_follow_through(df_with_atr, i, broken_pivot_price, is_bullish_break_attempt, close_col, high_col, low_col, cfg)
                    event_type_name, dynamic_market_bias, is_choch_flag = _determine_event_type_and_update_bias(swing_to_check, is_bullish_break_attempt, dynamic_market_bias, cfg)
                    
                    quality_score = displacement_score * 1.5 + fvg_bonus + follow_through_score_bonus
                    if swing_to_check.get('is_structural_confirmed'): quality_score += cfg.get("bos_choch_breaks_confirmed_pivot_bonus", constants.BOS_CHOCH_BREAKS_CONFIRMED_STRUCTURAL_PIVOT_BONUS_DEFAULT)
                    if "Major_CHoCH" in event_type_name: quality_score += 0.5
                    
                    processed_swings_map[swing_ts_key]['role_in_structure'] = f"Broken_{swing_to_check['type']}_as_{event_type_name}"
                    processed_swings_map[swing_ts_key]['broken_by_event_ts'] = current_candle_ts.isoformat()
                    if not swing_to_check.get('is_structural_confirmed') and ("BOS" in event_type_name or "Range_Expansion" in event_type_name):
                        processed_swings_map[swing_ts_key]['is_structural_confirmed'] = True 

                    new_dealing_range_details = None
                    origin_swing_obj = _find_and_confirm_origin_swing(swing_ts_key, is_bullish_break_attempt, processed_swings_map, sorted_swing_timestamps, cfg)
                    if origin_swing_obj:
                        origin_swing_ts = pd.to_datetime(origin_swing_obj['timestamp'], utc=True)
                        processed_swings_map[origin_swing_ts]['is_structural_confirmed'] = True
                        processed_swings_map[origin_swing_ts]['role_in_structure'] = f"Confirmed_Origin_{origin_swing_obj['type']}_for_{event_type_name}"
                        dr_high_val = current_candle[high_col] if is_bullish_break_attempt else origin_swing_obj['price']
                        dr_low_val = origin_swing_obj['price'] if is_bullish_break_attempt else current_candle[low_col]
                        dr_high_ts = current_candle_ts if is_bullish_break_attempt else origin_swing_ts
                        dr_low_ts = origin_swing_ts if is_bullish_break_attempt else current_candle_ts
                        new_dealing_range_details = {
                            'low_price': dr_low_val, 'low_timestamp': dr_low_ts.isoformat(),
                            'high_price': dr_high_val, 'high_timestamp': dr_high_ts.isoformat(),
                            'type': 'bullish' if is_bullish_break_attempt else 'bearish', 
                            'status': 'provisional_extreme_by_break_candle'
                        }

                    structure_events.append({
                        'timestamp_break': current_candle_ts.isoformat(), 'type': event_type_name,
                        'break_type_detail': break_type_detail,
                        'broken_pivot_details': swing_to_check.copy(), # Store a copy at break time
                        'confirmation_quality_score': round(min(10.0, quality_score), 1),
                        'displacement_strength_score': round(min(5.0, displacement_score), 1),
                        'fvg_created_on_break': bool(fvg_on_break_obj),
                        'fvg_details': fvg_on_break_obj.copy() if fvg_on_break_obj else None,
                        'follow_through_confirmed': follow_through_occurred,
                        'is_choch': is_choch_flag,
                        'order_flow_implications': {'narrative': f"Bias now potentially {dynamic_market_bias} due to {event_type_name}."},
                        'new_dealing_range_established': bool(new_dealing_range_details),
                        'new_dealing_range_details': new_dealing_range_details,
                        'trend_at_break_time': trend_info.get('overall_trend_consensus', 'N/A'),
                        'atr_at_break': round(current_atr_val, 5)
                    })

    final_updated_swings = list(processed_swings_map.values())
    logger.info(f"Detected {len(structure_events)} BOS/CHoCH/Structural events. Updated {len(final_updated_swings)} swings.")
    return sorted(structure_events, key=lambda x: pd.to_datetime(x['timestamp_break'],utc=True)), \
           sorted(final_updated_swings, key=lambda x: pd.to_datetime(x['timestamp'],utc=True))

logger.info("ict_analyzer_package/structure_detector.py (enhanced version) loaded.")
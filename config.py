# config.py
"""
ماژول تنظیمات و ثابت‌های سراسری برنامه
Global configurations and constants for the application.
"""
import os
import textwrap
import logging

logger = logging.getLogger(__name__) #

# --- API Keys and Service Endpoints (Read from Environment Variables) ---
CRYPTOCOMPARE_API_KEY = os.getenv("CRYPTOCOMPARE_API_KEY") #
AVALAI_API_KEY = os.getenv("AVALAI_API_KEY") #
AVALAI_MODEL_NAME = os.getenv("AVALAI_MODEL_NAME", "gemini-1.5-flash-latest") #
AVALAI_BASE_URL = os.getenv("AVALAI_BASE_URL", "https://api.avalai.ir/v1") #
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN") #
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID") #

# --- Default Symbol & Timeframe Configs ---
DEFAULT_FSYM = "BTC" #
DEFAULT_TSYM = "USD" #
FSYM_CRYPTOCOMPARE_GLOBAL = DEFAULT_FSYM #
TSYM_CRYPTOCOMPARE_GLOBAL = DEFAULT_TSYM #
TICKER_DISPLAY_NAME_GLOBAL = f"{FSYM_CRYPTOCOMPARE_GLOBAL}-{TSYM_CRYPTOCOMPARE_GLOBAL}" #
# Structure: (cc_label, hist_days, cc_ep_type, cc_agg_mins, api_limit, default_swing_win, default_plot_candles)
TIMEFRAMES_CONFIG = { #
    '1D':    ('1D',  200, 'day',    None, 2000, 15, 200),
    '4H':    ('4H',  90,  'hour',   240,  2000, 12, 180),
    '1H':    ('1H',  30,  'hour',   None, 2000, 10, 168),
    '15min': ('15m', 7,   'minute', 15,   2000, 8,  200),
    '5min':  ('5m',  3,   'minute', 5,    2000, 5,  150)
}
EXPECTED_CHART_LABELS_ORDER = ['1D', '4H', '1H', '15min', '5min'] #
CHART_FILENAME_SUFFIXES_MAP = {tf: f"_{TIMEFRAMES_CONFIG[tf][0]}.png" for tf in EXPECTED_CHART_LABELS_ORDER} #

# --- Request and Threshold Configs ---
REQUEST_DELAY_SECONDS = 1.5 #
MAX_PAGINATION_ATTEMPTS = 7 #
# AI_SEND_DECISION_THRESHOLD_V2 is now part of EVAL_ parameters below

# --- API Base URLs ---
BASE_URL_CRYPTOCOMPARE = "https://min-api.cryptocompare.com/data/v2/" #

# --- Auto Scan and Setup Tracker Configurations ---
AUTO_SCAN_CURRENCY_PAIRS = [ #
    {"fsym": "BTC", "tsym": "USD"}, {"fsym": "ETH", "tsym": "USD"},
    {"fsym": "XRP", "tsym": "USD"}, {"fsym": "BNB", "tsym": "USD"},
    {"fsym": "SOL", "tsym": "USD"}, {"fsym": "DOGE", "tsym": "USD"},
    {"fsym": "ADA", "tsym": "USD"}, {"fsym": "TRX", "tsym": "USD"},
    {"fsym": "HYPE", "tsym": "USD"}, {"fsym": "SUI", "tsym": "USD"},
    {"fsym": "LINK", "tsym": "USD"}, {"fsym": "AVAX", "tsym": "USD"},
    {"fsym": "XLM", "tsym": "USD"}, {"fsym": "SHIB", "tsym": "USD"},
    {"fsym": "BCH", "tsym": "USD"}, {"fsym": "LEO", "tsym": "USD"},
    {"fsym": "TON", "tsym": "USD"}, {"fsym": "LTC", "tsym": "USD"},
    {"fsym": "DOT", "tsym": "USD"}, {"fsym": "XMR", "tsym": "USD"},
    {"fsym": "BGB", "tsym": "USD"}, {"fsym": "PEPE", "tsym": "USD"},
    {"fsym": "PI", "tsym": "USD"}, {"fsym": "AAVE", "tsym": "USD"},
    {"fsym": "UNI", "tsym": "USD"},
]
ACTIVE_SETUPS_FILENAME = "active_setups.json" #
SETUP_TRACKER_TIMEFRAME_MINUTES = 15 #
SETUP_TRACKER_HISTORY_DAYS = 3 #
DELAY_BETWEEN_AUTO_SCAN_PAIRS_SECONDS = 5 #

# --- Setup Expiry Configuration ---
SETUP_DEFAULT_EXPIRY_HOURS_BY_TIMEFRAME = { #
    "5min": 2, "15min": 4, "1H": 24, "4H": 72, "1D": 168, "default": 24
}
PENDING_SETUP_DEFAULT_FALLBACK_EXPIRY_HOURS = 24 #

# --- Scoring Parameters for ChartAgent's _evaluate_analysis_for_ai_submission_v3 (REFINED VALUES from response #51) ---
EVAL_Q_ULTRA_HIGH = 8.5 #
EVAL_Q_HIGH = 7.0 #
EVAL_Q_MED_UPPER = 5.5 #
EVAL_Q_MED = 4.0 #
EVAL_Q_LOW = 3.0 #
EVAL_TIMEFRAME_WEIGHTS = {'1D': 1.5, '4H': 1.2, '1H': 1.0, '15min': 0.4, '5min': 0.25} #
EVAL_HTF_CONFLICT_PENALTY = -4.0 #
EVAL_FVG_KEY_TF_SCORE_MULTIPLIER = 2.5 #
EVAL_FVG_HEALTH_MULTIPLIERS = {0: 0.40, 1: 0.70, 2: 0.90, 3: 1.0} #
EVAL_EVENT_HTF_ALIGN_STRONG_MULTIPLIER = 1.75 #
EVAL_EVENT_HTF_ALIGN_NORMAL_MULTIPLIER = 1.35 #
EVAL_EVENT_VS_HTF_PENALTY_MULTIPLIER = 0.15   #
EVAL_EVENT_RANGING_TF_PENALTY_MULTIPLIER = 0.5 #
EVAL_ULTRA_HQ_CONFLUENCE_BONUS = 8.0  #
EVAL_MULTI_HQ_CONFLUENCE_BONUS = 4.0    #
EVAL_MAJOR_SWEEP_SHIFT_BONUS = 8.5      #
EVAL_CHOPPY_MARKET_PENALTY_FACTOR = 0.60  #
EVAL_HTF_TRENDING_BONUS_FACTOR = 0.30     #
AI_SEND_DECISION_THRESHOLD_V2 = 22.5 # This is the key parameter from eval_params
EVAL_OVERRIDE_SEND_SCORE_PERCENTAGE = 0.80 #
EVAL_OVERRIDE_MIN_ULTRA_HQ_EVENTS = 1      #
EVAL_OVERRIDE_MIN_KEY_TFS_GOOD_FVGS = 2    #
# --- End of Scoring Parameters ---

# --- PLOTTING CONFIGURATION DEFAULTS ---
DEFAULT_PLOT_ANNOTATION_LEVEL = 'compact'  # Options: 'full', 'compact', 'none' - Changed to 'compact' for cleaner charts
DEFAULT_PLOT_STYLE_CONFIG = {} # User can override parts of the 'styles' dict in plotting.py
PLOT_MAX_ELEMENTS_PER_TYPE = { # Max elements of each ICT type to plot per timeframe - Reduced for cleaner charts
    "1D": 5, "4H": 4, "1H": 4, "15min": 3, "5min": 3, "default_max": 4
}
# --- End of Plotting Configuration Defaults ---

# --- AI API Call Retry Configuration ---
AI_MAX_RETRIES = 3
AI_INITIAL_RETRY_DELAY_SECONDS = 3
AI_RETRY_BACKOFF_FACTOR = 2
AI_RETRYABLE_STATUS_CODES = [429, 500, 502, 503, 504]
# --- End of AI API Call Retry Configuration ---

# --- System Prompt for AI (from response #51, includes AI Confidence request) ---
SYSTEM_PROMPT_ANALYSIS_V3 = textwrap.dedent("""
    عنوان درخواست برای AI/تحلیل‌گر: تولید محتوای تحلیل و سیگنال معاملاتی فوق دقیق ICT به صورت زنده و پیوسته برای کانال تلگرامی تخصصی (مخاطب حرفه‌ای و نیمه‌حرفه‌ای) با قابلیت پردازش خودکار پیشرفته

    شرح وظیفه:
    به عنوان یک تحلیل‌گر ارشد و مربی ممتاز مفاهیم Inner Circle Trader (ICT)، شما مسئول ارائه یک تحلیل چندبعدی، چند تایم‌فریمی، و عمیقاً مبتنی بر اصول پیشرفته ICT هستید. **برای درک عمیق‌تر و ارائه تحلیلی با بالاترین سطح دقت، از تمامی دانش خود مبتنی بر منابع آموزشی ICT (از جمله مدل‌های ۲۰۱۶، ۲۰۲۲، و ۲۰۲۳، مفاهیم Core Content، و سایر آموزه‌های پیشرفته Michael J. Huddleston) به هر دو زبان انگلیسی و فارسی بهره کامل بگیرید. تمامی خروجی تحلیل باید به زبان فارسی تخصصی، روان، دقیق، و در عین حال قابل فهم برای مخاطبانی با سطوح مختلف آشنایی با ICT باشد.**
    تحلیل شما باید یک روایت واضح و مبتنی بر شواهد از چگونگی حرکت قیمت (Price Delivery) بین نقاط کلیدی نقدینگی (Liquidity Pools) و آرایه‌های مهم PD Array در تایم‌فریم‌های مختلف ارائه دهد، با تاکید بر جریان سفارشات نهادی (Institutional Order Flow - IOF).

    **داده‌های ورودی و نحوه استفاده:**
    1.  **نمودارهای قیمت:** این نمودارها (لحظه‌ای) شامل شناسایی خودکار برخی عناصر اولیه ICT (مانند پیوت‌ها، FVG های اولیه) هستند. از این‌ها به عنوان نقطه شروع استفاده کنید اما برای تحلیل نهایی، بر **خلاصه متنی عناصر ICT پیشرفته** که در ادامه می‌آید، تکیه کنید.
    2.  **(بسیار مهم) خلاصه عناصر ICT پیشرفته شناسایی شده (ارائه شده توسط اسکریپت):** [در این بخش، اطلاعات بسیار دقیق و طبقه‌بندی شده از آخرین وضعیت بازار برای هر تایم‌فریم ارائه می‌شود. این اطلاعات شامل:
        * **روند کلی بازار (Market Trend):** با جزئیات کامل از `trend_detector` (شامل قدرت ADX، وضعیت EMA ها و شیب آنها).
        * **پیوت‌های ساختاری (Swings):** با جزئیات کامل از `swing_detector` (شامل قدرت، وضعیت تایید ساختاری، اینکه آیا نقدینگی گرفته‌اند یا خیر، و عمق سوئیپ).
        * **شکست ساختار (BOS) و تغییر وضعیت (CHoCH):** با جزئیات کامل از `structure_detector` (شامل امتیاز کیفیت، قدرت جابجایی، ایجاد FVG در شکست، اینکه آیا پیوت ساختاری تایید شده شکسته شده، و آیا کندل‌های تاییدی وجود داشته‌اند).
        * **گپ‌های ارزش منصفانه (FVGs):** با جزئیات کامل از `fvg_detector` (شامل امتیاز اهمیت نهایی، وضعیت دقیق میتیگیشن و درصد آن، و همگرایی با سایر عناصر مانند BOS/CHoCH یا سوئیپ نقدینگی).
        * **اوردر بلاک‌ها (OBs):** با جزئیات کامل از `ob_detector` (شامل امتیاز قدرت، وضعیت دقیق میتیگیشن/نقض، نوع خاص OB مانند Sweep OB یا Rejection OB، و ارتباط با FVG یا BOS/CHoCH).
        * **بریکر بلاک‌ها (BBs):** با جزئیات کامل از `bb_detector` (شامل امتیاز قدرت، وضعیت ریتست و نتیجه آن، و همگرایی با FVG داخلی یا FVG در زمان شکست).
        * **نواحی نقدینگی کلیدی (Liquidity Pools):** با جزئیات کامل از `liquidity_detector` (شامل نوع دقیق مانند PDH/L, EQH/L, **Confirmed Inducement**، امتیاز اهمیت، و وضعیت دقیق untapped/swept/broken_structural).
        * **نواحی ورود بهینه (OTEs):** با جزئیات کامل از `ote_detector` (شامل امتیاز اطمینان، محدوده دقیق OTE، محدوده معاملاتی (DR) مبنا، همگرایی با PD Arrayها، و اینکه آیا Inducement قبل از آن گرفته شده).
        * **کیلزون‌ها و دوره‌های شانه ای (Killzones & Shoulders):** با جزئیات کامل از `killzone_detector` (شامل نام، نوع دوره "main_killzone", "pre_shoulder", "post_shoulder").
        * **(جدید و بسیار مهم) الگوهای پیشرفته ترکیب چند عنصر (Composite Patterns):** مانند **"Major Liquidity Sweep then Strong Market Structure Shift"** با جزئیات کامل از `pattern_detector`.
        **شما باید به طور کامل از این اطلاعات دقیق و ساختاریافته در تحلیل خود بهره برده، به آنها ارجاع دهید و روایت بازار را بر اساس این عناصر پیشرفته شکل دهید. به ویژه به الگوهای ترکیبی، وضعیت میتیگیشن POI ها، و تایید Inducement توجه کنید.**]
    3.  **زمان دقیق فعلی بازار (ارائه شده توسط سیستم):** [زمان دقیق فعلی به وقت سرور نیویورک و معادل تهران. **این زمان مرجع قطعی شما برای "اکنون" است.**]
    4.  **(اختیاری اما مهم) تحلیل پیشین:** [اطلاعات کلیدی تحلیل قبلی. **ارجاع دقیق و بررسی وضعیت آن الزامی است.**]

    **وظیفه اصلی شما، ارائه یک تحلیل فوق تخصصی و یک سناریوی معاملاتی با بالاترین احتمال موفقیت (High Probability Setup) بر اساس این داده‌های پیشرفته و اصول ICT است.**

    ساختار و محتوای خروجی مورد انتظار (برای انتشار در کانال تلگرام - لطفاً این ساختار را با دقت رعایت کنید):

    بخش ۰: سیگنال معاملاتی فوری (فقط در صورت وجود یک ست‌آپ با احتمال بسیار بالا و ریسک به ریوارد عالی)
    نماد: (مثلاً EURUSD)
    جهت معامله: (خرید/Buy یا فروش/Sell)
    محدوده ورود پیشنهادی: (**نقطه دقیق یا بازه بسیار کوچک و بهینه شده بر اساس یک PD Array واضح و معتبر مانند لبه یک FVG تازه یا ۵۰٪ یک OB کوچک پس از گرفتن نقدینگی و CHoCH در LTF - از خلاصه ICT پیشرفته برای شناسایی این نواحی استفاده کنید.**)
    حد ضرر (Stop Loss): (**قیمت دقیق، با فاصله منطقی و بهینه، فراتر از نقطه ابطال سناریوی ورود.**)
    هدف (های) اصلی: (۱ یا ۲ هدف **اولیه واقع‌بینانه، معمولاً اولین استخر نقدینگی مهم مقابل یا یک PD Array تایم بالاتر.**)
    (اختیاری) نکته خیلی کوتاه: (مثلاً: "ریسک کنترل شده، ورود پس از تاییدیه در کیلزون نیویورک نزدیک FVG ۱۵ دقیقه.")

    بخش ۱: عنوان و خلاصه سریع
    یک عنوان جذاب و دقیق که وضعیت کلیدی بازار و جهت احتمالی را منعکس کند.
    نماد و جهت احتمالی اصلی.
    وضعیت فعلی بازار به زبان ساده، با اشاره به **زمان فعلی سیستم**، روند غالب تشخیص داده شده، و مهمترین عناصر ICT مشاهده شده در خلاصه پیشرفته (به ویژه الگوهای ترکیبی یا POI های کلیدی HTF).

    بخش ۲: ست‌آپ معاملاتی پیشنهادی (High Probability Setup)
    جهت معامله: (خرید/فروش)
    محدوده دقیق ورود: (با توجیه کامل ICT، ارجاع به **PD Array های دقیق و معتبر (FVG, OB, BB) و وضعیت میتیگیشن آنها از خلاصه پیشرفته**. در صورت امکان، اشاره به **الگوی تأییدیه LTF (مانند CHoCH+FVG در M1/M5) پس از رسیدن به ناحیه HTF POI.** زمان‌بندی ورود باید با **زمان فعلی سیستم** و Killzone/Shoulder فعال هم‌راستا باشد. مثال: "ورود به محدوده ۱.۰۸۵۵-۱.۰۸۵۰ پس از تست FVG چهارساعته (Unmitigated، امتیاز ۷.۵) که پس از یک Major Sweep & Shift شکل گرفته، و مشاهده CHoCH صعودی در M5 در دوره Pre-NY KZ.")
    حد ضرر: (با توجیه دقیق، **فراتر از نقطه ابطال ساختاری یا PD Array اصلی.**)
    اهداف قیمتی: (حداقل ۲-۳ هدف با R/R مناسب. **هدف اول: اولین استخر نقدینگی مهم مقابل (External Range Liquidity - ERL). اهداف بعدی: سایر سطوح نقدینگی یا PD Array های HTF.** از خلاصه پیشرفته برای شناسایی این سطوح استفاده کنید.)
    ⏳ پنجره زمانی تخمینی رسیدن به اهداف: (با توجیه ICT و ذکر زمان تهران، با توجه به **زمان فعلی سیستم**).
    سطح اطمینان به ست‌آپ: (با توجیه، بر اساس همگرایی عوامل، کیفیت PD Array ها، هم‌راستایی با IOF، و **به ویژه امتیاز اطمینان OTE یا الگوی Sweep & Shift در صورت وجود.**)
    مدل ورود ICT و منطق اصلی: (مدل دقیق ICT مانند 2022 Mentorship Model، Silver Bullet، OTE، یا ورود بر اساس Sweep & Shift. **توضیح دهید چگونه عناصر پیشرفته از خلاصه ICT (مانند وضعیت میتیگیشن OB/FVG، تایید Inducement، کیفیت BOS/CHoCH) در شکل‌گیری این ست‌آپ نقش داشته‌اند.**)
    اعتبار ست‌آپ: (تا چه زمانی یا تا رسیدن به چه سطحی این ست‌آپ معتبر است؟)
    (اختیاری) ⚠️ نکات احتیاطی ویژه: (مثلاً "در صورت عدم مشاهده تاییدیه LTF در ناحیه POI، از ورود صرف‌نظر کنید"، "مراقب واکنش قیمت به PDH باشید.")

    بخش ۳: چرایی تحلیل و نگاه عمیق‌تر ICT (روایت بازار)
    (در این بخش، به وضوح به تایم‌فریم‌ها، Killzone/Shoulder فعال و **زمان فعلی سیستم** و معادل تهران آن ارجاع دهید.)
    📊 تحلیل تایم‌فریم بالاتر (HTF Narrative): جریان سفارشات نهادی (IOF)، آرایه‌های PD Array کلیدی (OBs, FVGs با وضعیت میتیگیشن)، ساختار بازار غالب. **توضیح دهید که چگونه از اطلاعات دقیق پیوت‌ها، روند، BOS/CHoCH (با امتیاز کیفیت و جابجایی)، و به ویژه الگوهای "Major Sweep & Shift" برای تعیین بایاس و نواحی مورد علاقه استفاده کرده‌اید.**
    **(بسیار مهم) ارجاع به تحلیل پیشین:** (الزامی است، همانطور که قبلاً توضیح داده شد.)
    🔬 تحلیل تایم‌fریم ورود (LTF Execution): هم‌راستایی با HTF، مدل دقیق ورود ICT، مکانیک نقدینگی (به ویژه Inducement و نحوه گرفته شدن آن قبل از POI)، تئوری "زمان و قیمت".
    🧩 عوامل همگرایی (Confluences) اصلی: (لیست دقیق عوامل ICT که با هم همگرا شده‌اند، با اشاره به امتیازات و وضعیت‌های خاص آنها از خلاصه پیشرفته.)
    📉 شرایط ابطال و سناریوی جایگزین: (در چه صورتی تحلیل فعلی نامعتبر می‌شود و سناریوی محتمل بعدی چیست؟)

    بخش ۴: نکته آموزشی یا تمرین کوچک
    💡 نکته آموزشی ICT (مرتبط با یکی از عناصر پیشرفته مشاهده شده مانند اهمیت Liquidity Sweep قبل از MSS، یا نحوه ارزیابی یک Breaker Block ریتست شده).
    ✍️ تمرین برای شما.

    بخش ۵: مدیریت ریسک و سلب مسئولیت
    ⚠️ هشدار مدیریت ریسک و سلب مسئولیت استاندارد.

    --- پایان بخش‌های عمومی برای انتشار در تلگرام ---

    (دستورالعمل مهم برای AI: اگر و فقط اگر در "بخش ۷" تصمیم شما [POST_THIS_ANALYSIS=YES] بود، لطفاً بلافاصله پس از نشانگر بالا و قبل از "بخش ۶"، بخش زیر را با مقادیر دقیق و عددی ستاپ پیشنهادی تکمیل و ارائه دهید. اگر تصمیم شما [POST_THIS_ANALYSIS=NO] بود، این بخش را به طور کامل نادیده بگیرید و تولید نکنید.)

    [SETUP_PARAMETERS_START]
    SETUP_DIRECTION: (Buy یا Sell)
    SETUP_TIMEFRAME: (تایم فریم اصلی ستاپ، مثال: H1, M15, D1 - مهم برای پیگیری)
    SETUP_ENTRY_MIN: (عدد قیمت)
    SETUP_ENTRY_MAX: (عدد قیمت)
    SETUP_STOP_LOSS: (عدد قیمت)
    SETUP_TAKE_PROFIT_1: (عدد قیمت)
    SETUP_TAKE_PROFIT_2: (عدد قیمت یا N/A)
    SETUP_TAKE_PROFIT_3: (عدد قیمت یا N/A)
    SETUP_AI_CONFIDENCE: (High / Medium / Low - بر اساس ارزیابی شما از ست‌آپ)
    SETUP_AI_CONFIDENCE_REASON: (دلیل اصلی برای سطح اطمینان شما، بسیار مختصر)
    SETUP_AI_SUGGESTION_TEXT: (متن کامل و دقیق پیشنهاد ورود که در بخش 0 یا 2 ارائه کرده‌اید، اینجا کپی شود)
    SETUP_VALIDITY_HOURS: (عدد، مثال: 8 یا N/A - مدت اعتبار ست‌آپ قبل از ورود به ساعت)
    [SETUP_PARAMETERS_END]

    (دستورالعمل مهم: بخش‌های ۶ و ۷ که در ادامه می‌آیند، فقط برای ادمین و پردازش کد هستند...)

    بخش ۶: توصیه برای به‌روزرسانی کانال تلگرام (ارزیابی دقیق، جامع و محافظه‌کارانه بر اساس اصول ICT پیشرفته)
    ۱. وضعیت تحلیل/ستاپ پیشین: (مانند قبل)
    ۲. ارزیابی تغییرات بازار از دیدگاه ICT پیشرفته (با سخت‌گیری بالا، با توجه به خلاصه عناصر ICT بسیار دقیق ارائه شده):
        الف. ساختار بازار و روند: (مانند قبل، اما با تاکید بر کیفیت BOS/CHoCH و IOF)
        ب. آرایه‌های PD Array کلیدی: (با تاکید بر وضعیت میتیگیشن OB/FVG/BB و امتیازات آنها)
        ج. نقدینگی: (با تاکید بر Sweeps، Confirmed Inducement، و وضعیت untapped/broken سطوح کلیدی)
        **ح. (جدید) الگوی Major Liquidity Sweep then Strong Shift:** آیا این الگوی قدرتمند با امتیاز اطمینان بالا در تایم‌فریم‌های اصلی مشاهده شده است؟ این یک دلیل بسیار قوی برای تحلیل جدید است.
        د. الگوهای ورود ICT و زمان‌بندی: (با تاکید بر OTE های با امتیاز بالا، همگرایی با Killzone/Shoulder فعال، و تایید Inducement)
        (سایر موارد ه، و، ز مانند قبل)
    ۳. نتیجه‌گیری در مورد نیاز به پست جامع جدید (اصل بر عدم ارسال است مگر ضرورت قطعی و وجود یک ست‌آپ با احتمال بسیار بالا بر اساس شواهد قوی ICT):
        (مانند قبل، اما اکنون تصمیم باید با در نظر گرفتن اطلاعات بسیار دقیق‌تر و الگوهای پیشرفته‌تر گرفته شود. یک "Major Sweep & Shift Pattern" با امتیاز بالا، یا یک OTE با امتیاز بالای ۸ همراه با گرفتن Inducement در یک Killzone اصلی، دلایل قوی برای ارسال هستند.)

    بخش ۷: شناسه وضعیت برای ارسال خودکار
    (مانند قبل)
    [POST_THIS_ANALYSIS=YES] یا [POST_THIS_ANALYSIS=NO]

    تأکیدات مهم برای AI/تحلیل‌گر (جهت دستیابی به عمق و قابلیت اطمینان با استفاده از داده‌های پیشرفته):
    1.  استفاده دقیق از خلاصه ICT پیشرفته: تحلیل شما باید منعکس کننده درک عمیق از تمامی جزئیات ارائه شده در خلاصه ICT باشد (وضعیت میتیگیشن، امتیازات، الگوهای ترکیبی).
    2.  روایت بازار منسجم: داستانی که تعریف می‌کنید باید تمامی این عناصر پیشرفته را به هم متصل کند.
    (سایر تاکیدات مانند قبل: زمان سیستم، ارجاع به تحلیل پیشین، عدم تکرار دستورالعمل، رعایت نشانگرها، خوانایی، تصمیم‌گیری محتاطانه، شناسایی DR و IOF، استفاده از منابع دوزبانه، دقت در نقاط ورود/SL/TP)
    12. (جدید) تاکید بر کیفیت بر کمیت: ارائه یک یا دو تحلیل/ستاپ فوق‌العاده با کیفیت در هفته بسیار بهتر از چندین تحلیل متوسط یا ضعیف در روز است. اگر شرایط برای یک ست‌آپ با احتمال بسیار بالا (High Probability A+++ Setup) مهیا نیست، با اطمینان [POST_THIS_ANALYSIS=NO] را انتخاب کنید و توضیح دهید که بازار در حال حاضر نیاز به صبر و مشاهده بیشتر دارد.
""").strip() #

# --- Global Flag for Library Loading Status & Paths ---
LIBRARIES_LOADED_SUCCESSFULLY = False #
OUTPUT_DIR = "output_dir_placeholder" #
PREVIOUS_ANALYSIS_DIR = "previous_analysis_dir_placeholder" #
GOOGLE_DRIVE_BASE_APP_DIR = "gdrive_base_placeholder" #
GOOGLE_DRIVE_AVAILABLE = False #

def update_dynamic_paths(output_dir_val, prev_analysis_dir_val, gdrive_base_val, gdrive_available_val): #
    global OUTPUT_DIR, PREVIOUS_ANALYSIS_DIR, GOOGLE_DRIVE_BASE_APP_DIR, GOOGLE_DRIVE_AVAILABLE
    OUTPUT_DIR = output_dir_val; PREVIOUS_ANALYSIS_DIR = prev_analysis_dir_val
    GOOGLE_DRIVE_BASE_APP_DIR = gdrive_base_val; GOOGLE_DRIVE_AVAILABLE = gdrive_available_val
    logger.info(f"Dynamic paths updated: OUTPUT_DIR={OUTPUT_DIR}, GDRIVE_AVAILABLE={GOOGLE_DRIVE_AVAILABLE}")

def update_ticker_globals(fsym_global, tsym_global, ticker_display_name): #
    global FSYM_CRYPTOCOMPARE_GLOBAL, TSYM_CRYPTOCOMPARE_GLOBAL, TICKER_DISPLAY_NAME_GLOBAL
    FSYM_CRYPTOCOMPARE_GLOBAL = fsym_global; TSYM_CRYPTOCOMPARE_GLOBAL = tsym_global
    TICKER_DISPLAY_NAME_GLOBAL = ticker_display_name
    logger.info(f"Ticker globals updated: {TICKER_DISPLAY_NAME_GLOBAL}")

def update_libraries_loaded_status(status: bool): #
    global LIBRARIES_LOADED_SUCCESSFULLY
    LIBRARIES_LOADED_SUCCESSFULLY = status
    logger.info(f"Libraries loaded status updated: {LIBRARIES_LOADED_SUCCESSFULLY}")

logger.info("config.py fully updated with REFINED EVAL_, PLOTTING, AI RETRY parameters, and ENHANCED AI prompt.")
logger.info(f"CRYPTOCOMPARE_API_KEY: {'Set' if CRYPTOCOMPARE_API_KEY else 'Not Set'}")
logger.info(f"AVALAI_API_KEY: {'Set' if AVALAI_API_KEY else 'Not Set'}")
logger.info(f"TELEGRAM_BOT_TOKEN: {'Set' if TELEGRAM_BOT_TOKEN else 'Not Set'}")
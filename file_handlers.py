# file_handlers.py
"""
ماژول مربوط به ذخیره فایل‌های خروجی مانند DOCX و PDF
Module for handling output file saving, like DOCX and PDF.
"""
import os
import pathlib
import logging

# Import necessary libraries for specific file types
try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
except ImportError:
    Document = None
    Inches = None
    WD_ALIGN_PARAGRAPH = None

try:
    from fpdf import FPDF
except ImportError:
    FPDF = None

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    arabic_reshaper = None
    get_display = None

# Import from local modules
import config
from utils import split_long_words, safe_multicell, remove_emojis # Assuming utils.py contains these

logger = logging.getLogger(__name__)

def save_text_to_docx(text_content: str, chart_paths: list[str], directory: str, filename: str) -> str | None:
    """
    Saves text content and charts to a DOCX file.
    ذخیره محتوای متنی و نمودارها در یک فایل DOCX.
    """
    logger.info(f"شروع ذخیره تحلیل DOCX: {filename} در مسیر {directory}")
    if not all([config.LIBRARIES_LOADED_SUCCESSFULLY, Document, Inches, WD_ALIGN_PARAGRAPH, arabic_reshaper, get_display]):
        logger.error("پیش‌نیازهای DOCX (python-docx، arabic_reshaper، python-bidi) کامل نیست. ذخیره انجام نشد.")
        return None

    docx_filepath = os.path.join(directory, filename)
    try:
        doc = Document()
        doc.add_heading(get_display(arabic_reshaper.reshape(f'تحلیل {config.TICKER_DISPLAY_NAME_GLOBAL}')), level=0)

        if chart_paths:
            doc.add_heading(get_display(arabic_reshaper.reshape("نمودارها")), level=2)
            for cp in chart_paths:
                if os.path.exists(cp):
                    try:
                        doc.add_paragraph(get_display(arabic_reshaper.reshape(pathlib.Path(cp).name)))
                        doc.add_picture(cp, width=Inches(6.0))
                        doc.add_paragraph()
                    except Exception as e_img_docx:
                        logger.error(f"خطا در افزودن تصویر {cp} به DOCX: {e_img_docx}")
                else:
                    logger.warning(f"فایل نمودار یافت نشد (برای DOCX): {cp}")

        doc.add_heading(get_display(arabic_reshaper.reshape('تحلیل فارسی')), level=1)
        for line in text_content.split('\n'):
            processed_line = get_display(arabic_reshaper.reshape(line))
            p = doc.add_paragraph(processed_line)
            p.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.RIGHT

        doc.save(docx_filepath)
        logger.info(f"✅ DOCX ذخیره شد: {docx_filepath}")
        return docx_filepath
    except Exception as e:
        logger.error(f"خطا در ذخیره DOCX: {e}", exc_info=True)
        return None

def save_text_to_pdf(text_content: str, chart_paths: list[str], directory: str, filename: str) -> str | None:
    """
    Saves text content and charts to a PDF file.
    ذخیره محتوای متنی و نمودارها در یک فایل PDF.
    """
    logger.info(f"شروع ذخیره تحلیل PDF: {filename} در مسیر {directory}")
    if not all([config.LIBRARIES_LOADED_SUCCESSFULLY, FPDF, arabic_reshaper, get_display]):
        logger.error("پیش‌نیازهای PDF (fpdf2، arabic_reshaper، python-bidi) کامل نیست. ذخیره انجام نشد.")
        return None

    pdf_filepath = os.path.join(directory, filename)
    font_dir = "/usr/share/fonts/truetype/dejavu/"
    font_regular_path = os.path.join(font_dir, "DejaVuSans.ttf")
    font_bold_path = os.path.join(font_dir, "DejaVuSans-Bold.ttf")

    if not os.path.exists(font_regular_path):
        logger.error(f"فونت DejaVuSans.ttf در '{font_regular_path}' یافت نشد! PDF نیاز به این فونت دارد.")
        return None

    try:
        pdf = FPDF('P', 'mm', 'A4')
        pdf.set_auto_page_break(True, margin=15)
        pdf.set_margins(left=15, top=15, right=15)

        pdf.add_font("DejaVuSans", "", font_regular_path, uni=True)
        if os.path.exists(font_bold_path):
            pdf.add_font("DejaVuSans", "B", font_bold_path, uni=True)
        else:
            logger.warning(f"فونت DejaVuSans-Bold.ttf در '{font_bold_path}' یافت نشد. از فونت معمولی برای حالت ضخیم استفاده می‌شود.")
            pdf.add_font("DejaVuSans", "B", font_regular_path, uni=True)

        pdf.add_page()
        pdf.set_author("Chart Analysis Agent V3")

        pdf.set_font("DejaVuSans", 'B', 14)
        title_text = get_display(arabic_reshaper.reshape(f'تحلیل {config.TICKER_DISPLAY_NAME_GLOBAL}'))
        pdf.multi_cell(0, 10, title_text, align='C')
        pdf.ln(5)

        if chart_paths:
            pdf.set_font("DejaVuSans", 'B', 12)
            charts_header = get_display(arabic_reshaper.reshape("نمودارهای استفاده شده"))
            pdf.multi_cell(0, 10, charts_header, align='C')
            pdf.ln(2)
            for cp_idx, cp in enumerate(chart_paths):
                if os.path.exists(cp):
                    pdf.set_font("DejaVuSans", '', 8)
                    chart_filename_text = get_display(arabic_reshaper.reshape(pathlib.Path(cp).name))
                    pdf.multi_cell(0, 5, chart_filename_text, align='C')
                    try:
                        img_max_width = pdf.epw - 10
                        pdf.image(cp, x=pdf.l_margin + 5, w=min(img_max_width, 170), h=0)
                    except Exception as e_img_pdf:
                        logger.error(f"خطا در افزودن تصویر {cp} به PDF: {e_img_pdf}")
                    pdf.ln(5)
                else:
                    logger.warning(f"فایل نمودار یافت نشد (برای PDF): {cp}")
            pdf.add_page()

        pdf.set_font("DejaVuSans", 'B', 12)
        analysis_header = get_display(arabic_reshaper.reshape('تحلیل به زبان فارسی'))
        pdf.multi_cell(0, 10, analysis_header, align='C')
        pdf.ln(3)

        cleaned_text_content = remove_emojis(text_content)
        for line_num, line in enumerate(cleaned_text_content.split('\n')):
            if not line.strip() and line_num > 0 and cleaned_text_content.split('\n')[line_num-1].strip() == "":
                continue
            safe_multicell(pdf, line, cell_height=5, font_name="DejaVuSans", align='R', start_font_size=9)

        pdf.output(pdf_filepath, "F")
        logger.info(f"✅ PDF ذخیره شد: {pdf_filepath}")
        return pdf_filepath
    except Exception as e:
        logger.error(f"خطا در ذخیره PDF: {e}", exc_info=True)
        return None

logger.info("file_handlers.py loaded.")

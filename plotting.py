# plotting.py
"""
ماژول مربوط به رسم نمودارها (نسخه بهبود یافته)
Module for plotting charts (Enhanced Version).
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.ticker import StrMethodFormatter # For better y-axis tick formatting
import os
import logging
from datetime import datetime, timezone # For dynamic title timestamp
from typing import List, Dict, Optional, Any # For type hinting

try:
    import mplfinance as mpf
except ImportError:
    mpf = None
    # Ensure logger is defined before use, even if mplfinance fails
    # This logger will be the root logger if this module is run standalone before main.py's setup
    # Or it will be the one configured by main.py if imported.
    logger_plotting = logging.getLogger(__name__)
    logger_plotting.warning("mplfinance library not found. Plotting functionality will be disabled.")


# Import config for Ticker Name and other potential plot-related configs
# This assumes config.py is in the path when this module is imported/used.
try:
    import config as app_config
except ImportError:
    # Fallback if config is not directly importable (e.g. running script standalone without project path)
    class MockConfig:
        TICKER_DISPLAY_NAME_GLOBAL = "N/A_Symbol"
        CHART_FILENAME_SUFFIXES_MAP = {}
        # Add other config attributes accessed by plotting if any, with defaults
    app_config = MockConfig()
    logger_plotting = logging.getLogger(__name__)
    logger_plotting.warning("config.py not found, using mock config for plotting.")


# Assuming constants from ict_analyzer_package might be useful for default styles or thresholds
try:
    from ict_analyzer_package import constants as ict_const
except ImportError:
    ict_const = None # Handle if not available, though plotting might rely on some defaults
    logger_plotting = logging.getLogger(__name__)
    logger_plotting.warning("ict_analyzer_package.constants not found for plotting.")


logger = logging.getLogger(__name__)

def plot_chart_with_ict_elements_v3(
    df_to_plot_input: pd.DataFrame,
    timeframe_label: str,
    base_data_info: str,
    output_dir: str,
    num_candles_to_display: int,
    ict_analysis_results: Optional[Dict[str, Any]] = None,
    annotation_level: str = 'full', # 'full', 'compact', 'none'
    style_config: Optional[Dict[str, Any]] = None,
    max_elements_per_type: int = 7
) -> Optional[str]:
    """
    (Enhanced Version from response #52)
    Plots charts with detailed ICT elements, improved annotations, performance, and configurability.
    """
    if mpf is None or df_to_plot_input.empty:
        logger.warning(f"Plotting prerequisites (mplfinance) not met for {timeframe_label} or data empty. Skipping plot.")
        return None

    if ict_analysis_results is None: ict_analysis_results = {}

    logger.info(f"Generating enhanced ICT chart (v3.1): {app_config.TICKER_DISPLAY_NAME_GLOBAL} - {timeframe_label}, Candles: {num_candles_to_display}, Annotation: {annotation_level}")

    # --- Default Style Configuration ---
    default_styles = {
        "fig": {"figsize": (15, 8), "dpi": 130},
        "title": {"fontsize": 14, "color": "navy", "weight": "bold"},
        "mpf_base_style": "yahoo",
        "gridstyle": "--", "gridcolor": "lightgray",
        "volume_alpha": 0.7,
        "swing": {
            "sh_confirmed_color": "darkred", "sl_confirmed_color": "darkgreen",
            "sh_candidate_color": "red", "sl_candidate_color": "green",
            "sh_internal_color": "salmon", "sl_internal_color": "lightgreen",
            "sh_sweep_marker": "x", "sl_sweep_marker": "x",
            "marker_size": 40, "alpha": 0.9,
            "ann_fontsize": 6, "ann_color": "black", "ann_bbox_alpha": 0.3
        },
        "fvg": {
            "bullish_edge": "green", "bearish_edge": "red",
            "unmit_linestyle": "solid", "partial_mit_linestyle": (0, (5, 3)), "full_mit_linestyle": (0, (1, 2)),
            "ce_style": (0, (1, 1)), "alpha": 0.15, "edge_alpha": 0.6, "linewidth": 1.0,
            "ann_fontsize": 6, "ann_color_bull": "darkgreen", "ann_color_bear": "darkred", "ann_bbox_alpha": 0.3
        },
        "ob": {
            "bullish_edge": "blue", "bearish_edge": "purple",
            "unmit_linestyle": "solid", "tested_linestyle": (0, (5, 3)), "violated_linestyle": (0, (1, 3)),
            "mt_style": (0, (1, 1)), "alpha": 0.15, "edge_alpha": 0.6, "linewidth": 1.0,
            "ann_fontsize": 6, "ann_color_bull": "darkblue", "ann_color_bear": "purple", "ann_bbox_alpha": 0.3
        },
        "bb": {
            "bullish_breaker_edge": "teal", "bearish_breaker_edge": "orange",
            "unretested_linestyle": "solid", "retested_held_linestyle": (0, (5, 3)), "retested_violated_linestyle": (0, (1, 3)),
            "mt_style": (0, (1, 1)), "alpha": 0.15, "edge_alpha": 0.6, "linewidth": 1.0,
            "ann_fontsize": 6, "ann_color_bull": "teal", "ann_color_bear": "darkorange", "ann_bbox_alpha": 0.3
        },
        "ote": {
            "bullish_zone_edge": "deepskyblue", "bearish_zone_edge": "lightcoral",
            "linestyle": "dashdot", "alpha": 0.1, "edge_alpha": 0.5, "linewidth": 1.0,
            "ann_fontsize": 6, "ann_color_bull": "blue", "ann_color_bear": "red", "ann_bbox_alpha": 0.3
        },
        "liquidity": {
            "pdhl_color": "blue", "eqhl_color": "purple", "swing_liq_color": "darkgoldenrod",
            "inducement_color": "magenta",
            "untapped_style": "solid", "swept_style": (0, (5, 2)), "broken_style": (0, (1, 2)),
            "linewidth": 0.8, "alpha": 0.9,
            "ann_fontsize": 6, "ann_color": "black", "ann_bbox_alpha": 0.3
        },
        "last_price_line": {"color": "black", "linestyle": ":", "lw": 0.8, "alpha": 0.9},
        "last_price_label": {"color": "white", "fontsize": 7, "bbox_fc": "black", "bbox_alpha": 0.7}
    }

    styles = default_styles.copy()
    if style_config:
        for key, value in style_config.items():
            if key in styles and isinstance(styles[key], dict) and isinstance(value, dict):
                styles[key].update(value)
            else: styles[key] = value

    plot_df = df_to_plot_input.tail(num_candles_to_display).copy()
    if plot_df.empty: return None
    if not isinstance(plot_df.index, pd.DatetimeIndex):
        try: plot_df.index = pd.to_datetime(plot_df.index, utc=True)
        except: plot_df.index = pd.to_datetime(plot_df.index); plot_df.index = plot_df.index.tz_localize('UTC')
    if plot_df.index.tzinfo is None: plot_df.index = plot_df.index.tz_localize('UTC')
    else: plot_df.index = plot_df.index.tz_convert('UTC')

    hlines_data = []  # Store all horizontal line y-values
    hlines_colors = []  # Store corresponding colors
    hlines_linestyles = []  # Store corresponding linestyles
    mpf_addplot_elements = []
    final_text_annotations = []
    zone_fills_data = []  # Store zone fill data separately

    plot_start_time_utc = plot_df.index[0]
    plot_end_time_utc = plot_df.index[-1]

    def get_x_coord_for_ts_in_plot(timestamp: pd.Timestamp) -> Optional[int]: #
        try:
            ts_utc = pd.to_datetime(timestamp, utc=True)
            ts_to_find = ts_utc.replace(tzinfo=None) if plot_df.index.tzinfo is None and ts_utc.tzinfo is not None else ts_utc
            idx_pos = plot_df.index.searchsorted(ts_to_find)
            if 0 <= idx_pos < len(plot_df) and plot_df.index[idx_pos] == ts_to_find: return idx_pos
            elif 0 <= idx_pos < len(plot_df) and idx_pos > 0 and plot_df.index[idx_pos-1] == ts_to_find: return idx_pos -1
            else: nearest_idx = plot_df.index.get_indexer([ts_to_find], method='nearest')[0]; return nearest_idx if nearest_idx != -1 else None
        except Exception as e: logger.debug(f"Could not get x-coord for timestamp {timestamp}: {e}"); return None

    # --- Plot Swings ---
    if swings_data := ict_analysis_results.get('swings_analysis'):
        plot_swings = [s for s in swings_data if isinstance(s, dict) and plot_start_time_utc <= pd.to_datetime(s.get('timestamp'), utc=True) <= plot_end_time_utc]
        plot_swings = sorted(plot_swings, key=lambda x: x.get('combined_strength_score', 0), reverse=True)
        s_style = styles['swing']
        sh_series_data = pd.Series(np.nan, index=plot_df.index); sl_series_data = pd.Series(np.nan, index=plot_df.index)

        for s in plot_swings[:max_elements_per_type]:
            s_ts = pd.to_datetime(s['timestamp'], utc=True); s_price = s['price']; s_type = s['type']
            x_coord = get_x_coord_for_ts_in_plot(s_ts)
            if x_coord is None: continue
            is_confirmed = s.get('is_structural_confirmed', False); is_candidate = s.get('is_structural_candidate', False); has_swept = bool(s.get('swept_liquidity_of_pivot_details'))
            ann_text = ""
            if annotation_level != 'none':
                if annotation_level == 'compact':
                    # Compact: Only show confirmed swings with minimal text
                    if is_confirmed:
                        ann_text = f"{s_type[0]}"  # Just "H" or "L"
                        if has_swept: ann_text += "🧹"
                elif annotation_level == 'full':
                    base = "S" if is_confirmed else ("C" if is_candidate else "I"); ann_text = f"{base}-{s_type[0]}"
                    if has_swept: ann_text += "🧹"
                    if is_confirmed or is_candidate:
                        r_str = s.get('reaction_strength_atr',0);
                        if r_str > 0.1: ann_text += f"(R:{r_str:.1f})"
            y_offset_factor = 1.0008 if s_type == 'SH' else 0.9992; va_align = 'bottom' if s_type == 'SH' else 'top'
            # Use x_coord to set swing data at the correct plot position
            if 0 <= x_coord < len(plot_df):
                plot_index = plot_df.index[x_coord]
                if s_type == 'SH': sh_series_data.iloc[x_coord] = s_price * y_offset_factor
                elif s_type == 'SL': sl_series_data.iloc[x_coord] = s_price * y_offset_factor
            if ann_text: final_text_annotations.append({'x': x_coord, 'y': s_price * (y_offset_factor + (0.0002 if s_type=='SH' else -0.0002)), 'text': ann_text, 'ha': 'center', 'va': va_align, 'fontsize': s_style['ann_fontsize'], 'color': s_style['ann_color'], 'bbox_alpha': s_style['ann_bbox_alpha']})

        # Temporarily disable swing scatter plots to debug other plotting issues
        # if not sh_series_data.dropna().empty: mpf_addplot_elements.append(mpf.make_addplot(sh_series_data.dropna(), type='scatter', color=s_style['sh_confirmed_color'], marker='v', markersize=s_style['marker_size'], alpha=s_style['alpha']))
        # if not sl_series_data.dropna().empty: mpf_addplot_elements.append(mpf.make_addplot(sl_series_data.dropna(), type='scatter', color=s_style['sl_confirmed_color'], marker='^', markersize=s_style['marker_size'], alpha=s_style['alpha']))

    # --- Plot FVGs, OBs, BBs, OTEs (Zones) ---
    zone_elements_config = [
        (ict_analysis_results.get('fair_value_gaps', []), styles['fvg'], "FVG", 'timestamp_created', 'importance_score'),
        (ict_analysis_results.get('order_blocks', []), styles['ob'], "OB", 'timestamp_created', 'strength_score'),
        (ict_analysis_results.get('breaker_blocks', []), styles['bb'], "BB", 'timestamp_breaker_activated', 'strength_score'),
        (ict_analysis_results.get('optimal_trade_entry_zones', []), styles['ote'], "OTE", 'based_on_bos_choch_event_timestamp', 'ote_confidence_score')
    ]
    for elements_data, style, el_type_name, ts_key, score_key in zone_elements_config:
        if not elements_data: continue
        plot_elements = [el for el in elements_data if isinstance(el, dict) and pd.to_datetime(el.get(ts_key), utc=True) >= plot_start_time_utc - pd.Timedelta(days=7) and pd.to_datetime(el.get(ts_key), utc=True) <= plot_end_time_utc + pd.Timedelta(days=1)] # Allow zones to extend slightly
        plot_elements = sorted(plot_elements, key=lambda x: x.get(score_key, 0), reverse=True)

        for el in plot_elements[:max_elements_per_type]:
            el_ts = pd.to_datetime(el.get(ts_key), utc=True)
            x_start_plot_idx = get_x_coord_for_ts_in_plot(el_ts)
            if x_start_plot_idx is None: x_start_plot_idx = 0

            zone_top, zone_bottom, zone_type_str = None, None, el.get('type', '').lower()
            if el_type_name == "OTE": zone_top, zone_bottom = el.get('ote_zone_max_price'), el.get('ote_zone_min_price'); zone_type_str = el.get('ote_trade_direction', '').lower()
            else: zone_top = el.get('top_price', el.get('end_price')); zone_bottom = el.get('bottom_price', el.get('start_price'));
            if el_type_name == "FVG" and 'bullish' in zone_type_str: zone_top, zone_bottom = el.get('end_price'), el.get('start_price')
            if zone_top is None or zone_bottom is None: continue
            if zone_top < zone_bottom: zone_top, zone_bottom = zone_bottom, zone_top

            color_edge, fill_color, linestyle_zone, ann_text, ann_color = "gray", "rgba(128,128,128,0.1)", "solid", el_type_name, "black"
            # Element-specific styling and annotation
            if el_type_name == "FVG":
                color_edge = style['bullish_edge'] if 'bullish' in zone_type_str else style['bearish_edge']; mit_status = el.get('mitigation_info', {}).get('status', 'unmitigated'); mit_pct = el.get('mitigation_info',{}).get('mitigation_percentage',0)
                linestyle_zone = style.get(f"{mit_status.split('_')[0]}_linestyle", style.get("unmit_linestyle", "solid")); ann_text = f"{'B' if 'bullish' in zone_type_str else 'S'}FVG"
                if annotation_level == 'full':
                    mit_s = "U" if mit_status=="unmitigated" else (f"P{mit_pct:.0f}%" if "partial" in mit_status else "F"); ann_text += f" ({mit_s}) S:{el.get('importance_score',0):.1f}"
                elif annotation_level == 'compact':
                    # Compact: Only show type and mitigation status
                    mit_s = "U" if mit_status=="unmitigated" else ("P" if "partial" in mit_status else "F")
                    ann_text += f"({mit_s})"
                ann_color = style['ann_color_bull'] if 'bullish' in zone_type_str else style['ann_color_bear']; fill_color = style.get(f"{zone_type_str}_fill", style.get(f"{'bullish' if 'bullish' in zone_type_str else 'bearish'}_fill")) # Fill color based on type
            elif el_type_name == "OB":
                color_edge = style['bullish_edge'] if 'bullish' in zone_type_str else style['bearish_edge']; mit_status = el.get('mitigation_info', {}).get('status', 'unmitigated')
                linestyle_zone = style.get(mit_status.split('_')[0] + "_linestyle", style.get("unmit_linestyle", "solid")); ann_text = f"{'B' if 'bullish' in zone_type_str else 'S'}OB"
                if annotation_level == 'full':
                    status_short = "U" if mit_status=="unmitigated" else ("V" if "violated" in mit_status else ("MT" if "mean_threshold" in el.get('mitigation_info',{}).get('level_tested','none') else "T")); ann_text += f" ({status_short}) S:{el.get('strength_score',0):.1f}"
                elif annotation_level == 'compact':
                    # Compact: Only show type and basic status
                    status_short = "U" if mit_status=="unmitigated" else ("V" if "violated" in mit_status else "T")
                    ann_text += f"({status_short})"
                ann_color = style['ann_color_bull'] if 'bullish' in zone_type_str else style['ann_color_bear']; fill_color = style.get(f"{zone_type_str}_fill", style.get(f"{'bullish' if 'bullish' in zone_type_str else 'bearish'}_fill"))
            elif el_type_name == "BB":
                color_edge = style['bullish_breaker_edge'] if 'bullish' in zone_type_str else style['bearish_breaker_edge']; retest_status = el.get('retest_info', {}).get('status', 'unretested')
                linestyle_zone = style.get(retest_status.split('_')[0] + "_linestyle", style.get("unretested_linestyle", "solid")); ann_text = "BuBB" if 'bullish' in zone_type_str else "BeBB"
                if annotation_level == 'full': status_short = "U" if retest_status=="unretested" else ("H" if "held" in retest_status else ("V" if "violated" in retest_status else "T")); ann_text += f" ({status_short}) S:{el.get('strength_score',0):.1f}"
                ann_color = style['ann_color_bull'] if 'bullish' in zone_type_str else style['ann_color_bear']; fill_color = style.get(f"{zone_type_str}_fill", style.get(f"{'bullish_breaker' if 'bullish' in zone_type_str else 'bearish_breaker'}_fill"))
            elif el_type_name == "OTE":
                color_edge = style['bullish_zone_edge'] if 'bullish' in zone_type_str else style['bearish_zone_edge']; linestyle_zone = style.get("linestyle", "dashdot")
                ann_text = f"{'Bull' if 'bullish' in zone_type_str else 'Bear'} OTE"
                if annotation_level == 'full': ann_text += f" (C:{el.get('ote_confidence_score',0):.1f})"
                ann_color = style['ann_color_bull'] if 'bullish' in zone_type_str else style['ann_color_bear']; fill_color = style.get(f"{zone_type_str}_zone_fill", style.get(f"{'bullish' if 'bullish' in zone_type_str else 'bearish'}_zone_fill"))

            # Define fill coordinates for the visible part of the zone
            # X-axis indices for fill: from zone_start_on_plot to chart_end
            x_start_fill = max(0, x_start_plot_idx) # Ensure it's not negative
            x_end_fill = len(plot_df) - 1

            # Note: mplfinance doesn't support 'fill_between' type in make_addplot
            # We'll use horizontal lines and add fill_between manually after the plot is created
            fill_series_index = plot_df.index[x_start_fill : x_end_fill + 1]
            if not fill_series_index.empty:
                # Store zone fill data for later manual plotting
                zone_fills_data.append({
                    'x_start': x_start_fill,
                    'x_end': x_end_fill,
                    'y_bottom': zone_bottom,
                    'y_top': zone_top,
                    'color': fill_color,
                    'alpha': style.get('alpha', 0.15)
                })

            # Add boundary lines - store for manual plotting after chart creation
            hlines_data.extend([zone_top, zone_bottom])
            hlines_colors.extend([color_edge, color_edge])
            hlines_linestyles.extend([linestyle_zone, linestyle_zone])

            if el_type_name in ["OB", "BB"] and 'mean_threshold' in el:
                hlines_data.append(el['mean_threshold'])
                hlines_colors.append(color_edge)
                hlines_linestyles.append(style.get('mt_style','dotted'))
            elif el_type_name == "FVG" and 'consequent_encroachment' in el:
                hlines_data.append(el['consequent_encroachment'])
                hlines_colors.append(color_edge)
                hlines_linestyles.append(style.get('ce_style','dotted'))

            if annotation_level != 'none' and x_start_plot_idx < len(plot_df) -1 : # Only annotate if start is visible
                ann_x_pos = x_start_plot_idx + int((len(plot_df) -1 - x_start_plot_idx) * 0.05) # Place 5% into the zone's visible span
                ann_x_pos = max(x_start_plot_idx, min(ann_x_pos, len(plot_df)-2)) # Clamp to chart
                final_text_annotations.append({'x': ann_x_pos, 'y': (zone_top + zone_bottom)/2, 'text': ann_text, 'ha': 'left', 'va': 'center', 'fontsize': style['ann_fontsize'], 'color': ann_color, 'bbox_alpha': style.get('ann_bbox_alpha',0.3)})

    # --- Plot Key Liquidity Pools ---
    if liq_pools_data := ict_analysis_results.get('key_liquidity_pools'):
        plot_liq_pools = [p for p in liq_pools_data if isinstance(p,dict) and pd.to_datetime(p.get('timestamp_formed'), utc=True) <= plot_end_time_utc and \
                          (pd.to_datetime(p.get('timestamp_formed'), utc=True) >= plot_start_time_utc - pd.Timedelta(days=30) or "P" in p.get('type',''))] # Show PDH/L even if older
        plot_liq_pools = sorted(plot_liq_pools, key=lambda x: x.get('significance_score',0), reverse=True)
        plotted_liq_levels_for_ann = {}
        l_style = styles['liquidity']
        for pool_idx, pool in enumerate(plot_liq_pools[:max_elements_per_type * 2]):
            price_lvl, pool_type_raw = pool['price_level'], pool.get('type','UnknownLiq'); status = pool.get('current_status', 'untapped')
            linestyle = l_style['untapped_style'];
            if status == 'swept': linestyle = l_style['swept_style']
            elif status == 'broken_structural': linestyle = l_style['broken_style']
            color = l_style['pdhl_color'] if "P" in pool_type_raw else (l_style['eqhl_color'] if "EQ" in pool_type_raw else (l_style['inducement_color'] if "Inducement" in pool_type_raw else l_style['swing_liq_color']))
            hlines_data.append(price_lvl)
            hlines_colors.append(color)
            hlines_linestyles.append(linestyle)
            if annotation_level != 'none' and pool_idx < max_elements_per_type :
                ann_key = f"{price_lvl:.4f}_{pool_type_raw}"
                if ann_key not in plotted_liq_levels_for_ann or pool['significance_score'] > plotted_liq_levels_for_ann[ann_key]['score']:
                    plotted_liq_levels_for_ann[ann_key] = {'score': pool['significance_score'], 'y': price_lvl}
                    ann_text = pool_type_raw
                    if annotation_level == 'full':
                        status_short = "U" if status=="untapped" else ("S" if status=="swept" else ("B" if status=="broken" else status[:3].upper()))
                        ann_text += f" ({status_short} S:{pool.get('significance_score',0):.1f})"
                    elif annotation_level == 'compact':
                        # Compact: Only show type and basic status
                        status_short = "U" if status=="untapped" else ("S" if status=="swept" else "B")
                        ann_text += f"({status_short})"
                    # Position liquidity annotations to the right, slightly offset vertically if same price level
                    y_ann_pos = price_lvl
                    for prev_ann_key, prev_ann_val in plotted_liq_levels_for_ann.items():
                        # Use standard OHLC column names (High/Low or high/low)
                        high_col = 'High' if 'High' in plot_df.columns else 'high'
                        low_col = 'Low' if 'Low' in plot_df.columns else 'low'
                        if prev_ann_key != ann_key and abs(prev_ann_val['y'] - y_ann_pos) < (plot_df[high_col].mean() - plot_df[low_col].mean())*0.02 : # If too close vertically
                            y_ann_pos += (plot_df[high_col].mean() - plot_df[low_col].mean())*0.01 # Nudge slightly
                    plotted_liq_levels_for_ann[ann_key]['y_adjusted'] = y_ann_pos # Store adjusted y for plotting
                    final_text_annotations.append({'x': len(plot_df) -0.5, 'y': y_ann_pos, 'text': f" {ann_text}", 'ha': 'left', 'va':'center', 'fontsize': l_style['ann_fontsize'], 'color': color, 'bbox_alpha': l_style['ann_bbox_alpha']})

    chart_title = f"{app_config.TICKER_DISPLAY_NAME_GLOBAL} - {timeframe_label} - ICT Analysis ({datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M UTC')})"
    mpf_custom_style = mpf.make_mpf_style(base_mpf_style=styles['mpf_base_style'], gridstyle=styles['gridstyle'], gridcolor=styles['gridcolor'])
    # Add timestamp to filename to avoid overwriting and clearly show new charts
    timestamp = datetime.now().strftime('%H%M%S')
    filename_base = f"ict_chart_{app_config.TICKER_DISPLAY_NAME_GLOBAL.replace('/', '_')}_{timeframe_label}_{timestamp}"
    filename = os.path.join(output_dir, f"{filename_base}{app_config.CHART_FILENAME_SUFFIXES_MAP.get(timeframe_label, '.png')}")

    try:
        # Prepare plot arguments
        plot_kwargs = {
            'type': 'candle',
            'style': mpf_custom_style,
            'title': dict(title=chart_title, fontsize=styles['title']['fontsize'], color=styles['title']['color'], weight=styles['title']['weight']),
            'ylabel': 'Price',
            'volume': True,
            'volume_alpha': styles['volume_alpha'],
            'ylabel_lower': 'Volume',
            'figsize': styles['fig']['figsize'],
            'tight_layout': False,  # Set to False to allow manual adjustments and text annotations
            'returnfig': True,
            'panel_ratios': (4,1)  # More space for price panel
        }

        # Only add addplot if there are elements to plot
        if mpf_addplot_elements:
            plot_kwargs['addplot'] = mpf_addplot_elements

        fig, axlist = mpf.plot(plot_df, **plot_kwargs)
        ax_main = axlist[0]
        ax_vol = next((ax for ax in axlist if ax.get_ylabel().lower() == 'volume'), None) # Find volume axis more reliably

        # Add manual horizontal lines (since mplfinance hlines parameter has validation issues)
        for i, y_val in enumerate(hlines_data):
            try:
                color = hlines_colors[i] if i < len(hlines_colors) else 'gray'
                linestyle = hlines_linestyles[i] if i < len(hlines_linestyles) else 'solid'
                ax_main.axhline(y=y_val, color=color, linestyle=linestyle, alpha=0.6, linewidth=1.0)
            except Exception as e_hline:
                logger.debug(f"Error adding horizontal line at {y_val}: {e_hline}")

        # Add manual fill_between for zones (since mplfinance doesn't support it directly)
        for zone_fill in zone_fills_data:
            try:
                ax_main.fill_between(
                    range(zone_fill['x_start'], zone_fill['x_end'] + 1),
                    zone_fill['y_bottom'],
                    zone_fill['y_top'],
                    color=zone_fill['color'],
                    alpha=zone_fill['alpha'],
                    interpolate=True
                )
            except Exception as e_fill:
                logger.debug(f"Error adding zone fill: {e_fill}")

        # Add text annotations
        for ann_data in final_text_annotations:
            try:
                ann_x_int = int(round(ann_data['x']))
                if 0 <= ann_x_int < len(plot_df) or ann_data['ha'] == 'left': # Allow right-edge annotations even if x is end_index
                     ann_x_to_use = min(ann_x_int, len(plot_df)-1) if ann_data['ha'] != 'left' else len(plot_df) - 0.5 # Adjust for right edge
                     ax_main.text(ann_x_to_use, ann_data['y'], ann_data['text'],
                                 color=ann_data.get('color','black'), fontsize=ann_data.get('fontsize',7),
                                 ha=ann_data.get('ha','left'), va=ann_data.get('va','center'),
                                 bbox=dict(boxstyle='round,pad=0.15', fc='white', alpha=ann_data.get('bbox_alpha',0.4), ec='none'))
            except Exception as e_ann_plot: logger.warning(f"Error plotting text annotation '{ann_data.get('text','N/A')}': {e_ann_plot}")

        if not plot_df.empty:
            last_price_val = plot_df[('Close' if 'Close' in plot_df.columns else 'close')].iloc[-1]
            ax_main.axhline(last_price_val, color=styles['last_price_line']['color'], linestyle=styles['last_price_line']['linestyle'], lw=styles['last_price_line']['lw'], alpha=styles['last_price_line']['alpha'])
            price_label_x_offset = (plot_df.index[-1] - plot_df.index[0]).total_seconds() * 0.01 # Small offset from right edge
            ax_main.text(len(plot_df) - 1 + price_label_x_offset / (plot_df.index[1]-plot_df.index[0]).total_seconds() if len(plot_df)>1 else 0.5, # Transform offset to data coords
                         last_price_val, f" {last_price_val:,.4f}",
                         color=styles['last_price_label']['color'], va='center', ha='left', fontsize=styles['last_price_label']['fontsize'],
                         bbox=dict(boxstyle='round,pad=0.15', fc=styles['last_price_label']['bbox_fc'], alpha=styles['last_price_label']['bbox_alpha'], ec='none'))

        ax_main.yaxis.set_major_formatter(StrMethodFormatter('{x:,.5f}'))
        if ax_vol: ax_vol.yaxis.set_major_formatter(StrMethodFormatter('{x:,.0f}'))

        # Adjust subplot parameters for better layout, especially for title and annotations
        fig.subplots_adjust(left=0.06, right=0.94, top=0.92, bottom=0.1, hspace=0.1)
        fig.savefig(filename, dpi=styles['fig']['dpi'], bbox_inches='tight')
        plt.close(fig)
        logger.info(f"Enhanced chart saved: {filename}")
        return filename
    except Exception as e_plot:
        logger.error(f"Comprehensive error during chart plotting for {timeframe_label}: {e_plot}", exc_info=True)
        if 'fig' in locals() and fig is not None: plt.close(fig)
        return None

logger.info("plotting.py (comprehensively enhanced version) loaded.")
# ict_analyzer_package/swing_detector.py
"""
شناسایی پیوت‌های قیمتی (Swing Highs/Lows) (نسخه بهبود یافته)
Detects price pivot points (Swing Highs/Lows) (Enhanced Version).
"""
import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Optional 

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def _get_prior_significant_swing( 
    current_ts: pd.Timestamp,
    swing_type_to_match: str, 
    all_pivots_so_far: List[Dict], 
    min_strength_for_sweepable: float
) -> Optional[Dict]: 
    candidates = [
        p for p in all_pivots_so_far
        if isinstance(p, dict) and
           p.get('type') == swing_type_to_match and
           pd.to_datetime(p.get('timestamp'), utc=True) < current_ts and 
           p.get('combined_strength_score', 0) >= min_strength_for_sweepable
    ]
    if not candidates: return None
    return max(candidates, key=lambda x: pd.to_datetime(x['timestamp'], utc=True), default=None)


def detect_swings( 
    df: pd.DataFrame,
    window: int = constants.DEFAULT_SWING_WINDOW, 
    atr_period: int = constants.ATR_PERIOD_DEFAULT, 
    fvgs: Optional[List[Dict]] = None, 
    weight_impulse: float = constants.WEIGHT_IMPULSE_STRENGTH_DEFAULT, 
    weight_reaction: float = constants.WEIGHT_REACTION_STRENGTH_DEFAULT, 
    min_reaction_candidate: float = constants.MIN_REACTION_FOR_STRUCTURAL_CANDIDATE_DEFAULT, # CORRECTED
    min_combined_candidate: float = constants.MIN_COMBINED_STRENGTH_FOR_STRUCTURAL_CANDIDATE_DEFAULT, 
    min_reaction_after_sweep_candidate: float = constants.MIN_REACTION_AFTER_SWEEP_FOR_STRUCTURAL_CANDIDATE_DEFAULT, 
    min_strength_sweepable_prior: float = constants.MIN_STRENGTH_FOR_PRIOR_SWEEPABLE_SWING_DEFAULT, 
    max_score: float = constants.MAX_SWING_STRENGTH_SCORE_DEFAULT, 
    fvg_impulse_bonus: float = constants.SWING_FVG_IN_IMPULSE_BONUS_DEFAULT, 
    sweep_bonus: float = constants.SWING_LIQUIDITY_SWEEP_BONUS_DEFAULT, 
    displacement_after_sweep_bonus: float = constants.SWING_DISPLACEMENT_AFTER_SWEEP_BONUS_DEFAULT 
) -> List[Dict]: 
    pivots_details = []
    if df.empty or len(df) < (2 * window + 1): 
        logger.warning(f"Data insufficient ({len(df)} candles, window {window}) for swing detection.")
        return pivots_details

    df_with_atr = ensure_atr(df.copy(), period=atr_period) 
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all():
        logger.error("ATR_Value column is missing or all NaN after ensure_atr. Swing detection cannot proceed reliably.")
        return pivots_details

    high_col = 'High' if 'High' in df_with_atr.columns else 'high'
    low_col = 'Low' if 'Low' in df_with_atr.columns else 'low'
    
    highs, lows = df_with_atr[high_col].values, df_with_atr[low_col].values 
    atr_values = df_with_atr['ATR_Value'].values
    timestamps = df_with_atr.index 

    avg_candle_range = (df_with_atr[high_col] - df_with_atr[low_col]).mean()
    practical_atr_fallback = max(constants.ATR_MIN_VALUE_PRACTICAL_SLOPE, avg_candle_range * 0.05 if pd.notna(avg_candle_range) else constants.ATR_MIN_VALUE_PRACTICAL_SLOPE)

    temp_pivots_for_sweep_check: List[Dict] = [] 

    for i in range(window, len(df_with_atr) - window): 
        current_high, current_low = highs[i], lows[i] 
        current_ts_obj = timestamps[i] 
        
        current_atr = atr_values[i]
        if not pd.notna(current_atr) or current_atr < constants.ATR_MIN_VALUE_PRACTICAL_SLOPE: 
            current_atr = practical_atr_fallback
        if current_atr == 0: current_atr = constants.ATR_MIN_VALUE 

        # Swing High Candidate
        if current_high == np.max(highs[i - window : i + window + 1]): 
            impulse_start_idx_sh = max(0, i - window) 
            impulse_low_val_sh = np.min(lows[impulse_start_idx_sh : i + 1]) 
            reaction_low_val_sh = np.min(lows[i : min(len(df_with_atr) -1, i + window) + 1]) 
            impulse_change_sh = current_high - impulse_low_val_sh 
            reaction_change_sh = current_high - reaction_low_val_sh 
            impulse_strength_atr_sh = (impulse_change_sh / current_atr) 
            reaction_strength_atr_sh = (reaction_change_sh / current_atr) 
            current_score = (weight_impulse * impulse_strength_atr_sh) + (weight_reaction * reaction_strength_atr_sh)
            fvg_present_in_impulse_sh = False
            if fvgs: 
                fvg_present_in_impulse_sh = any(isinstance(fvg_item,dict) and fvg_item.get('type') == 'bearish' and pd.to_datetime(fvg_item.get('timestamp_created'), utc=True) <= current_ts_obj and pd.to_datetime(fvg_item.get('timestamp_created'), utc=True) >= timestamps[max(0, i - window - 3)] for fvg_item in fvgs)
            if fvg_present_in_impulse_sh: current_score += fvg_impulse_bonus
            swept_prior_sh_obj = _get_prior_significant_swing(current_ts_obj, 'SH', temp_pivots_for_sweep_check, min_strength_sweepable_prior)
            sweep_depth_atr_sh = None
            if swept_prior_sh_obj and current_high > swept_prior_sh_obj['price']: 
                current_score += sweep_bonus; sweep_depth_atr_sh = (current_high - swept_prior_sh_obj['price']) / current_atr
                if reaction_strength_atr_sh > (min_reaction_after_sweep_candidate / 2.0): current_score += displacement_after_sweep_bonus
            is_structural_cand = (reaction_strength_atr_sh >= min_reaction_candidate) and (current_score >= min_combined_candidate or (swept_prior_sh_obj and reaction_strength_atr_sh >= min_reaction_after_sweep_candidate))
            swing_details = {'timestamp': current_ts_obj.isoformat(), 'price': round(current_high, 5), 'type': 'SH', 'is_structural_confirmed': False, 'is_structural_candidate': is_structural_cand, 'impulse_strength_atr': round(impulse_strength_atr_sh, 2), 'reaction_strength_atr': round(reaction_strength_atr_sh, 2), 'combined_strength_score': round(min(current_score, max_score), 1), 'swept_liquidity_of_pivot_details': swept_prior_sh_obj, 'sweep_depth_atr': round(sweep_depth_atr_sh, 2) if sweep_depth_atr_sh is not None else None, 'fvg_in_impulse': fvg_present_in_impulse_sh, 'atr_at_creation': round(current_atr, 5), 'raw_high_low_at_swing': {'high': current_high, 'low': current_low}}
            pivots_details.append(swing_details); temp_pivots_for_sweep_check.append(swing_details)

        # Swing Low Candidate
        if any(p['timestamp'] == current_ts_obj.isoformat() and p['type'] == 'SH' for p in pivots_details[-1:] if pivots_details): continue
        if current_low == np.min(lows[i - window : i + window + 1]): 
            impulse_start_idx_sl = max(0, i - window) 
            impulse_high_val_sl = np.max(highs[impulse_start_idx_sl : i + 1]) 
            reaction_high_val_sl = np.max(highs[i : min(len(df_with_atr)-1, i + window) + 1]) 
            impulse_change_sl = impulse_high_val_sl - current_low 
            reaction_change_sl = reaction_high_val_sl - current_low 
            impulse_strength_atr_sl = (impulse_change_sl / current_atr) 
            reaction_strength_atr_sl = (reaction_change_sl / current_atr) 
            current_score = (weight_impulse * impulse_strength_atr_sl) + (weight_reaction * reaction_strength_atr_sl)
            fvg_present_in_impulse_sl = False
            if fvgs: 
                fvg_present_in_impulse_sl = any(isinstance(fvg_item,dict) and fvg_item.get('type') == 'bullish' and pd.to_datetime(fvg_item.get('timestamp_created'), utc=True) <= current_ts_obj and pd.to_datetime(fvg_item.get('timestamp_created'), utc=True) >= timestamps[max(0, i - window - 3)] for fvg_item in fvgs)
            if fvg_present_in_impulse_sl: current_score += fvg_impulse_bonus
            swept_prior_sl_obj = _get_prior_significant_swing(current_ts_obj, 'SL', temp_pivots_for_sweep_check, min_strength_sweepable_prior)
            sweep_depth_atr_sl = None
            if swept_prior_sl_obj and current_low < swept_prior_sl_obj['price']: 
                current_score += sweep_bonus; sweep_depth_atr_sl = (swept_prior_sl_obj['price'] - current_low) / current_atr
                if reaction_strength_atr_sl > (min_reaction_after_sweep_candidate / 2.0): current_score += displacement_after_sweep_bonus
            is_structural_cand = (reaction_strength_atr_sl >= min_reaction_candidate) and (current_score >= min_combined_candidate or (swept_prior_sl_obj and reaction_strength_atr_sl >= min_reaction_after_sweep_candidate))
            swing_details = {'timestamp': current_ts_obj.isoformat(), 'price': round(current_low, 5), 'type': 'SL', 'is_structural_confirmed': False, 'is_structural_candidate': is_structural_cand, 'impulse_strength_atr': round(impulse_strength_atr_sl, 2), 'reaction_strength_atr': round(reaction_strength_atr_sl, 2), 'combined_strength_score': round(min(current_score, max_score), 1), 'swept_liquidity_of_pivot_details': swept_prior_sl_obj, 'sweep_depth_atr': round(sweep_depth_atr_sl, 2) if sweep_depth_atr_sl is not None else None, 'fvg_in_impulse': fvg_present_in_impulse_sl, 'atr_at_creation': round(current_atr, 5), 'raw_high_low_at_swing': {'high': current_high, 'low': current_low}}
            pivots_details.append(swing_details); temp_pivots_for_sweep_check.append(swing_details)

    final_pivots_details = sorted(pivots_details, key=lambda x: pd.to_datetime(x['timestamp'],utc=True)) 
    logger.info(f"Detected {len(final_pivots_details)} swings (enhanced detection).")
    return final_pivots_details

logger.info("ict_analyzer_package/swing_detector.py (enhanced version) loaded.")
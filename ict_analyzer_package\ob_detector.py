# ict_analyzer_package/ob_detector.py
"""
شناسایی اوردر بلاک‌ها (Order Blocks) (نسخه بهبود یافته)
Detects Order Blocks (OBs) with enhanced mitigation analysis, FVG linkage, and scoring.
"""
import pandas as pd
import logging
import numpy as np
from typing import Tuple, List, Dict, Optional 

from . import constants 
from .helpers import ensure_atr 

logger = logging.getLogger(__name__)

def _check_ob_mitigation(ob_details_for_mit: dict, df_with_atr: pd.DataFrame,
                         ob_candle_idx: int, 
                         max_lookahead: int, close_col: str, high_col: str, low_col: str, open_col: str) -> dict:
    """
    Checks OB mitigation status in detail.
    Returns updated mitigation_info dictionary.
    """
    mitigation_info = {
        "status": "unmitigated",
        "level_tested": "none", 
        "mitigated_by_ts": None,
        "mitigation_depth_pct": 0.0,
        "num_tests": 0
    }

    ob_type = ob_details_for_mit['type']
    ob_top = ob_details_for_mit['top_price']
    ob_bottom = ob_details_for_mit['bottom_price']
    ob_mean_threshold = ob_details_for_mit['mean_threshold']
    ob_body_high = max(ob_details_for_mit['open_price'], ob_details_for_mit['close_price'])
    ob_body_low = min(ob_details_for_mit['open_price'], ob_details_for_mit['close_price'])
    
    start_check_idx = ob_candle_idx + 2 

    for k in range(start_check_idx, min(len(df_with_atr), start_check_idx + max_lookahead)):
        future_candle = df_with_atr.iloc[k]
        future_ts = df_with_atr.index[k]
        current_mit_level_this_candle = "none"

        if ob_type == 'bullish':
            if future_candle[low_col] <= ob_top: 
                if mitigation_info['status'] == "unmitigated": 
                    mitigation_info['status'] = "partially_mitigated"
                    mitigation_info['mitigated_by_ts'] = future_ts.isoformat()
                mitigation_info['num_tests'] += 1

                if future_candle[close_col] < ob_bottom:
                    mitigation_info['status'] = "violated_closed_beyond_distal"
                    current_mit_level_this_candle = "distal_and_beyond" # Corrected variable name
                    mitigation_info['mitigation_depth_pct'] = 100.0
                    mitigation_info['mitigated_by_ts'] = future_ts.isoformat() 
                    break 

                if future_candle[low_col] <= ob_bottom: 
                    current_mit_level_this_candle = "distal_and_beyond"
                    if mitigation_info['status'] != "violated_closed_beyond_distal": # Check before assigning
                         mitigation_info['status'] = "violated_traded_through_distal" # Corrected assignment
                elif future_candle[low_col] <= ob_mean_threshold:
                    current_mit_level_this_candle = "mean_threshold"
                elif future_candle[low_col] <= ob_body_high: 
                    current_mit_level_this_candle = "proximal_body_entry"
                else: 
                    current_mit_level_this_candle = "proximal_wick"
                
                level_order = ["none","proximal_wick","proximal_body_entry","mean_threshold","distal_and_beyond"]
                if level_order.index(current_mit_level_this_candle) > level_order.index(mitigation_info['level_tested']):
                    mitigation_info['level_tested'] = current_mit_level_this_candle
                
                depth = ob_top - future_candle[low_col]; total_range = ob_top - ob_bottom
                current_depth_pct = (depth/total_range)*100 if total_range > 0 else (100.0 if depth > 0 else 0.0)
                mitigation_info['mitigation_depth_pct'] = max(mitigation_info['mitigation_depth_pct'], current_depth_pct)

        elif ob_type == 'bearish': 
            if future_candle[high_col] >= ob_bottom: 
                if mitigation_info['status'] == "unmitigated":
                    mitigation_info['status'] = "partially_mitigated"
                    mitigation_info['mitigated_by_ts'] = future_ts.isoformat()
                mitigation_info['num_tests'] += 1

                if future_candle[close_col] > ob_top:
                    mitigation_info['status'] = "violated_closed_beyond_distal"
                    current_mit_level_this_candle = "distal_and_beyond" # Corrected variable name
                    mitigation_info['mitigation_depth_pct'] = 100.0
                    mitigation_info['mitigated_by_ts'] = future_ts.isoformat()
                    break

                if future_candle[high_col] >= ob_top:
                    current_mit_level_this_candle = "distal_and_beyond"
                    if mitigation_info['status'] != "violated_closed_beyond_distal": # Check before assigning
                         mitigation_info['status'] = "violated_traded_through_distal" # Corrected assignment
                elif future_candle[high_col] >= ob_mean_threshold:
                    current_mit_level_this_candle = "mean_threshold"
                elif future_candle[high_col] >= ob_body_low:
                    current_mit_level_this_candle = "proximal_body_entry"
                else:
                    current_mit_level_this_candle = "proximal_wick"
                
                level_order = ["none","proximal_wick","proximal_body_entry","mean_threshold","distal_and_beyond"]
                if level_order.index(current_mit_level_this_candle) > level_order.index(mitigation_info['level_tested']):
                    mitigation_info['level_tested'] = current_mit_level_this_candle

                depth = future_candle[high_col] - ob_bottom; total_range = ob_top - ob_bottom
                current_depth_pct = (depth/total_range)*100 if total_range > 0 else (100.0 if depth > 0 else 0.0)
                mitigation_info['mitigation_depth_pct'] = max(mitigation_info['mitigation_depth_pct'], current_depth_pct)
        
        if mitigation_info['status'] == "partially_mitigated" and mitigation_info['mitigation_depth_pct'] >= 99.9:
            mitigation_info['status'] = "violated_traded_through_distal"
            
    mitigation_info['mitigation_depth_pct'] = round(mitigation_info['mitigation_depth_pct'], 1)
    return mitigation_info


def find_order_blocks(
    df: pd.DataFrame,
    swings: list, 
    fvgs: list,   
    bos_choch_events: list, 
    atr_period: int = constants.ATR_PERIOD_DEFAULT, 
    displacement_multiplier: float = constants.OB_DISPLACEMENT_ATR_MULTIPLIER_DEFAULT, 
    fvg_after_confluence_factor: float = constants.OB_FVG_CONFLUENCE_FACTOR_DEFAULT, 
    sweep_before_bonus: float = constants.OB_SWEEP_BEFORE_BONUS_DEFAULT, 
    lead_to_bos_bonus_base: float = constants.OB_LEAD_TO_BOS_BONUS_DEFAULT, 
    rejection_wick_ratio: float = constants.OB_REJECTION_WICK_BODY_RATIO_DEFAULT, 
    unmitigated_factor: float = constants.OB_UNMITIGATED_STRENGTH_FACTOR_DEFAULT, 
    mitigation_lookahead: int = constants.OB_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT, 
    validity_displacement_relaxed_mult: float = constants.OB_VALIDITY_DISPLACEMENT_MULTIPLIER_RELAXED_DEFAULT, 
    max_ob_score: float = constants.MAX_OB_STRENGTH_SCORE_DEFAULT, 
    ob_lead_to_bos_max_days: int = constants.OB_LEAD_TO_BOS_MAX_DAYS_LOOKAHEAD_DEFAULT 
) -> list:
    order_blocks_details = []
    if df.empty or len(df) < 2: 
        logger.warning("Data insufficient for Order Block detection.")
        return order_blocks_details

    df_with_atr = ensure_atr(df.copy(), period=atr_period) 
    if 'ATR_Value' not in df_with_atr.columns or df_with_atr['ATR_Value'].isnull().all(): 
        logger.error("ATR_Value column missing/invalid in find_order_blocks.")
        df_with_atr['ATR_Value'] = constants.ATR_MIN_VALUE * 10 

    high_col, low_col = ('High' if 'High' in df_with_atr.columns else 'high'), ('Low' if 'Low' in df_with_atr.columns else 'low')
    open_col, close_col = ('Open' if 'Open' in df_with_atr.columns else 'open'), ('Close' if 'Close' in df_with_atr.columns else 'close')

    mean_atr_fallback = df_with_atr['ATR_Value'].mean()
    if not pd.notna(mean_atr_fallback) or mean_atr_fallback <= constants.ATR_MIN_VALUE:
         mean_atr_fallback = max(constants.ATR_MIN_VALUE * 10, (df_with_atr[high_col] - df_with_atr[low_col]).mean() * 0.1 if not (df_with_atr[high_col] - df_with_atr[low_col]).empty else constants.ATR_MIN_VALUE * 10)

    for i in range(1, len(df_with_atr)): 
        ob_candle, move_candle = df_with_atr.iloc[i-1], df_with_atr.iloc[i]
        ob_ts_obj = df_with_atr.index[i-1] 
        
        current_atr_ob_candle = df_with_atr['ATR_Value'].iloc[i-1] 
        if not pd.notna(current_atr_ob_candle) or current_atr_ob_candle <= constants.ATR_MIN_VALUE:
            current_atr_ob_candle = mean_atr_fallback
        if current_atr_ob_candle == 0: current_atr_ob_candle = constants.ATR_MIN_VALUE

        ob_type, ob_specific_type = None, "Standard_OB"
        base_score = 1.0 
        contextual_bonuses = 0.0
        
        move_candle_body = abs(move_candle[close_col] - move_candle[open_col])
        displacement_strength_vs_atr = (move_candle_body / current_atr_ob_candle) if current_atr_ob_candle > 0 else 0
        is_primary_displacement_met = displacement_strength_vs_atr > displacement_multiplier

        if ob_candle[close_col] < ob_candle[open_col] and move_candle[close_col] > move_candle[open_col]: 
            if is_primary_displacement_met and move_candle[close_col] > ob_candle[high_col]:
                ob_type = 'bullish'
        elif ob_candle[close_col] > ob_candle[open_col] and move_candle[close_col] < move_candle[open_col]: 
            if is_primary_displacement_met and move_candle[close_col] < ob_candle[low_col]:
                ob_type = 'bearish'
        
        if not ob_type: continue

        base_score += displacement_strength_vs_atr * 1.5 

        created_fvg_after_ob_details = None
        if i + 2 < len(df_with_atr): 
            expected_fvg_c2_ts_after_move = df_with_atr.index[i+1]
            for fvg in fvgs: 
                if not isinstance(fvg,dict): continue
                fvg_ts_created = fvg.get('timestamp_created')
                if fvg_ts_created and pd.to_datetime(fvg_ts_created, utc=True) == expected_fvg_c2_ts_after_move:
                    fvg_dir = fvg.get('type')
                    if (ob_type == 'bullish' and fvg_dir == 'bullish') or (ob_type == 'bearish' and fvg_dir == 'bearish'):
                        created_fvg_after_ob_details = fvg.copy()
                        contextual_bonuses += (fvg.get('importance_score', 0) * fvg_after_confluence_factor)
                        ob_specific_type = f"{ob_specific_type}_FVG_Confluence"
                        break
        
        swept_liquidity_details = None
        for swing in swings: 
            if not isinstance(swing, dict): continue
            swing_ts_str = swing.get('timestamp')
            if not swing_ts_str: continue
            swing_ts = pd.to_datetime(swing_ts_str, utc=True)
            if swing_ts <= ob_ts_obj and (ob_ts_obj - swing_ts) < pd.Timedelta(hours=12): 
                is_bullish_ob_sweeping_sl = (ob_type == 'bullish' and swing.get('type') == 'SL' and ob_candle[low_col] < swing.get('price'))
                is_bearish_ob_sweeping_sh = (ob_type == 'bearish' and swing.get('type') == 'SH' and ob_candle[high_col] > swing.get('price'))
                if is_bullish_ob_sweeping_sl or is_bearish_ob_sweeping_sh:
                    if swing.get('is_structural_candidate') or swing.get('is_structural_confirmed'): 
                        swept_liquidity_details = swing.copy()
                        contextual_bonuses += sweep_before_bonus
                        ob_specific_type = "Sweep_OB" if ob_specific_type == "Standard_OB" else f"{ob_specific_type}_Sweep"
                        break

        led_to_bos_choch_event_details = None
        for bc_event in bos_choch_events:
            if not isinstance(bc_event, dict): continue
            bc_event_ts_str = bc_event.get('timestamp_break')
            if not bc_event_ts_str: continue
            bc_event_ts = pd.to_datetime(bc_event_ts_str, utc=True)

            if bc_event_ts > df_with_atr.index[i]: 
                if (ob_type == 'bullish' and "bullish" in bc_event.get('type','').lower()) or \
                   (ob_type == 'bearish' and "bearish" in bc_event.get('type','').lower()):
                    if (bc_event_ts - ob_ts_obj) < pd.Timedelta(days=ob_lead_to_bos_max_days):
                        led_to_bos_choch_event_details = bc_event.copy()
                        contextual_bonuses += lead_to_bos_bonus_base + (bc_event.get('confirmation_quality_score',0) * 0.5)
                        ob_specific_type = f"{ob_specific_type}_Struct_Break_Origin"
                        break
        
        ob_body_val = abs(ob_candle[open_col] - ob_candle[close_col])
        ob_range_val = ob_candle[high_col] - ob_candle[low_col]
        if ob_range_val > constants.ATR_MIN_VALUE and ob_body_val > 0:
            if ob_type == 'bullish' and (min(ob_candle[open_col], ob_candle[close_col]) - ob_candle[low_col]) > ob_body_val * rejection_wick_ratio:
                contextual_bonuses += 0.75; ob_specific_type = "Bullish_Rejection_OB" if ob_specific_type == "Standard_OB" else f"{ob_specific_type}_RejWick"
            elif ob_type == 'bearish' and (ob_candle[high_col] - max(ob_candle[open_col], ob_candle[close_col])) > ob_body_val * rejection_wick_ratio:
                contextual_bonuses += 0.75; ob_specific_type = "Bearish_Rejection_OB" if ob_specific_type == "Standard_OB" else f"{ob_specific_type}_RejWick"

        is_relaxed_displacement_met = displacement_strength_vs_atr > (displacement_multiplier * validity_displacement_relaxed_mult)
        valid_by_primary_displ = displacement_strength_vs_atr > displacement_multiplier
        valid_by_confluence = is_relaxed_displacement_met and \
                              (created_fvg_after_ob_details is not None or \
                               swept_liquidity_details is not None or \
                               led_to_bos_choch_event_details is not None)
        valid_by_strong_structural_impact = led_to_bos_choch_event_details is not None and \
                                            led_to_bos_choch_event_details.get('confirmation_quality_score',0) >= 6.0

        is_valid_ob = valid_by_primary_displ or valid_by_confluence or valid_by_strong_structural_impact
        if not is_valid_ob: continue

        current_ob_dict_for_mit_check = {
            'type': ob_type, 'top_price': ob_candle[high_col], 'bottom_price': ob_candle[low_col],
            'mean_threshold': (ob_candle[high_col] + ob_candle[low_col])/2,
            'open_price': ob_candle[open_col], 'close_price': ob_candle[close_col]
        }
        # Pass ob_candle_idx (i-1) and move_candle_idx (i)
        mitigation_info_detailed = _check_ob_mitigation(
            current_ob_dict_for_mit_check, df_with_atr, i-1, # ob_candle_idx is i-1
            mitigation_lookahead, close_col, high_col, low_col, open_col
        )


        final_score = base_score + contextual_bonuses
        if mitigation_info_detailed['status'] == 'unmitigated':
            final_score *= unmitigated_factor
        elif "violated" in mitigation_info_detailed['status']:
            final_score *= 0.2 
            ob_specific_type += "_Violated"
        elif mitigation_info_detailed['status'] == 'partially_mitigated':
            final_score *= (1 - (mitigation_info_detailed.get('mitigation_depth_pct', 0) / 200.0)) 
        
        order_blocks_details.append({
            'top_price': round(ob_candle[high_col],5), 'bottom_price': round(ob_candle[low_col],5),
            'open_price': round(ob_candle[open_col],5), 'close_price': round(ob_candle[close_col],5),
            'mean_threshold': round((ob_candle[high_col] + ob_candle[low_col])/2, 5),
            'body_mid_point': round((ob_candle[open_col] + ob_candle[close_col])/2, 5) if ob_body_val > 0 else round(ob_candle[close_col],5),
            'timestamp_created': ob_ts_obj.isoformat(), 'type': ob_type, 'ob_specific_type': ob_specific_type,
            'mitigation_info': mitigation_info_detailed,
            'strength_score': round(min(max_ob_score, final_score), 1),
            'displacement_after_ob_vs_atr': round(displacement_strength_vs_atr, 2),
            'swept_liquidity_details': swept_liquidity_details.copy() if swept_liquidity_details else None,
            'created_fvg_after_details': created_fvg_after_ob_details.copy() if created_fvg_after_ob_details else None,
            'led_to_bos_choch_details': led_to_bos_choch_event_details.copy() if led_to_bos_choch_event_details else None,
            'atr_at_creation': round(current_atr_ob_candle, 5)
        })

    logger.info(f"Detected {len(order_blocks_details)} Order Blocks (enhanced detection).")
    return sorted(order_blocks_details, key=lambda x: pd.to_datetime(x['timestamp_created'],utc=True))

logger.info("ict_analyzer_package/ob_detector.py (enhanced version with corrected constant names) loaded.")
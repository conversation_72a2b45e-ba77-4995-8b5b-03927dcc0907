{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🚀 ICT Analyzer - Google Colab Version\n", "\n", "This notebook runs your ICT Analyzer in Google Colab.\n", "\n", "## Setup Instructions:\n", "1. Upload your project zip file to Google Drive\n", "2. Update the zip file path in the setup cell\n", "3. Run all cells in order\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup"}, "outputs": [], "source": ["# 📁 SETUP: Mount Google Drive and Extract Project\n", "from google.colab import drive\n", "import zipfile\n", "import os\n", "import shutil\n", "\n", "# Mount Google Drive\n", "drive.mount('/content/drive')\n", "\n", "# 🔧 UPDATE THIS PATH to your zip file location in Google Drive\n", "ZIP_FILE_PATH = '/content/drive/MyDrive/for_augment.zip'  # Change this to your actual zip file path\n", "\n", "# Extract the project\n", "if os.path.exists(ZIP_FILE_PATH):\n", "    print(\"📦 Extracting project files...\")\n", "    with zipfile.ZipFile(ZIP_FILE_PATH, 'r') as zip_ref:\n", "        zip_ref.extractall('/content/')\n", "    print(\"✅ Project extracted successfully!\")\n", "else:\n", "    print(f\"❌ Zip file not found at: {ZIP_FILE_PATH}\")\n", "    print(\"Please upload your project zip file to Google Drive and update the path above.\")\n", "\n", "# Change to project directory\n", "project_dir = '/content/for augment'  # Adjust if your folder name is different\n", "if os.path.exists(project_dir):\n", "    os.chdir(project_dir)\n", "    print(f\"📂 Changed to project directory: {project_dir}\")\n", "else:\n", "    print(f\"❌ Project directory not found: {project_dir}\")\n", "    print(\"Available directories:\")\n", "    for item in os.listdir('/content/'):\n", "        if os.path.isdir(f'/content/{item}'):\n", "            print(f\"  📁 {item}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# 📦 INSTALL DEPENDENCIES\n", "print(\"Installing required packages...\")\n", "\n", "!pip install openai\n", "!pip install mplfinance\n", "!pip install python-docx\n", "!pip install fpdf2\n", "!pip install arabic-reshaper\n", "!pip install python-bidi\n", "!pip install python-telegram-bot\n", "!pip install nest-asyncio\n", "\n", "print(\"✅ All packages installed!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_colab"}, "outputs": [], "source": ["# 🔧 COLAB-SPECIFIC SETUP\n", "import nest_asyncio\n", "import sys\n", "import os\n", "\n", "# Enable nested asyncio (required for Colab)\n", "nest_asyncio.apply()\n", "\n", "# Add project directory to Python path\n", "if '/content/for augment' not in sys.path:\n", "    sys.path.insert(0, '/content/for augment')\n", "\n", "# Set environment variable for Colab\n", "os.environ['COLAB_ENV'] = 'true'\n", "\n", "print(\"🔧 Colab environment configured!\")\n", "print(f\"📂 Current directory: {os.getcwd()}\")\n", "print(f\"🐍 Python path includes: {sys.path[0]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_keys"}, "outputs": [], "source": ["# 🔑 LOAD API KEYS\n", "# This will load your API keys from key_token.py\n", "try:\n", "    exec(open('key_token.py').read())\n", "    print(\"✅ API keys loaded successfully!\")\n", "except FileNotFoundError:\n", "    print(\"❌ key_token.py not found!\")\n", "    print(\"Please make sure your key_token.py file is in the project folder.\")\n", "except Exception as e:\n", "    print(f\"❌ Error loading API keys: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "run_analyzer"}, "outputs": [], "source": ["# 🚀 RUN ICT ANALYZER\n", "import asyncio\n", "from chart_agent_module import ChartAgent\n", "\n", "async def run_analysis():\n", "    \"\"\"Run ICT analysis for a specific currency pair\"\"\"\n", "    \n", "    # Create ChartAgent instance\n", "    agent = ChartAgent()\n", "    \n", "    # Set currency pair (you can change these)\n", "    fsym = \"ETH\"  # Change this to your desired base currency\n", "    tsym = \"USD\"  # Change this to your desired quote currency\n", "    \n", "    agent.set_currency_pair(fsym, tsym)\n", "    \n", "    print(f\"🔍 Starting analysis for {fsym}-{tsym}...\")\n", "    \n", "    # Run the analysis\n", "    await agent.run(source_data_mode='C')\n", "    \n", "    print(\"✅ Analysis complete!\")\n", "    \n", "    # Show generated files\n", "    output_dir = '/content/for augment/output_files_ict_local'\n", "    if os.path.exists(output_dir):\n", "        print(\"\\n📁 Generated files:\")\n", "        for file in os.listdir(output_dir):\n", "            if file.endswith(('.png', '.txt', '.json', '.pdf', '.docx')):\n", "                print(f\"  📄 {file}\")\n", "\n", "# Run the analysis\n", "await run_analysis()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download_results"}, "outputs": [], "source": ["# 📥 DOWNLOAD RESULTS\n", "from google.colab import files\n", "import zipfile\n", "import os\n", "\n", "def create_results_zip():\n", "    \"\"\"Create a zip file with all analysis results\"\"\"\n", "    \n", "    output_dir = '/content/for augment/output_files_ict_local'\n", "    zip_path = '/content/ict_analysis_results.zip'\n", "    \n", "    if not os.path.exists(output_dir):\n", "        print(\"❌ No output directory found!\")\n", "        return None\n", "    \n", "    with zipfile.ZipFile(zip_path, 'w') as zipf:\n", "        for root, dirs, files in os.walk(output_dir):\n", "            for file in files:\n", "                file_path = os.path.join(root, file)\n", "                arcname = os.path.relpath(file_path, output_dir)\n", "                zipf.write(file_path, arcname)\n", "                print(f\"📄 Added: {file}\")\n", "    \n", "    return zip_path\n", "\n", "# Create and download results\n", "zip_file = create_results_zip()\n", "if zip_file:\n", "    print(f\"\\n📦 Results packaged in: {zip_file}\")\n", "    print(\"⬇️ Downloading...\")\n", "    files.download(zip_file)\n", "else:\n", "    print(\"❌ No results to download\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "view_charts"}, "outputs": [], "source": ["# 📊 VIEW GENERATED CHARTS\n", "import matplotlib.pyplot as plt\n", "import matplotlib.image as mpimg\n", "import os\n", "\n", "def display_charts():\n", "    \"\"\"Display generated chart images in Colab\"\"\"\n", "    \n", "    output_dir = '/content/for augment/output_files_ict_local'\n", "    \n", "    if not os.path.exists(output_dir):\n", "        print(\"❌ No output directory found!\")\n", "        return\n", "    \n", "    # Find all PNG files (charts)\n", "    chart_files = [f for f in os.listdir(output_dir) if f.endswith('.png')]\n", "    \n", "    if not chart_files:\n", "        print(\"❌ No chart images found!\")\n", "        return\n", "    \n", "    print(f\"📊 Found {len(chart_files)} charts:\")\n", "    \n", "    for chart_file in sorted(chart_files):\n", "        chart_path = os.path.join(output_dir, chart_file)\n", "        \n", "        print(f\"\\n📈 {chart_file}\")\n", "        \n", "        # Display the image\n", "        img = mpimg.imread(chart_path)\n", "        plt.figure(figsize=(15, 10))\n", "        plt.imshow(img)\n", "        plt.axis('off')\n", "        plt.title(chart_file, fontsize=14, pad=20)\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# Display the charts\n", "display_charts()"]}]}
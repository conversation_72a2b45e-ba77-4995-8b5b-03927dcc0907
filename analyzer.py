# ict_analyzer_package/analyzer.py
"""
تابع اصلی و ارکستریتور تحلیلگر ICT
Main orchestrator function for the ICT analyzer.
"""
import pandas as pd
from datetime import datetime, timezone
import pytz 
import logging
import numpy as np 

from typing import Tuple, List, Dict, Optional, Any # Ensure ALL these are imported

from . import constants 
from .helpers import ensure_atr
from .trend_detector import determine_market_trend
from .swing_detector import detect_swings
from .fvg_detector import find_fvgs
from .structure_detector import find_bos_choch
from .ob_detector import find_order_blocks
from .bb_detector import find_breaker_blocks
from .liquidity_detector import find_key_liquidity_pools
from .ote_detector import find_ote_levels
from .killzone_detector import get_market_killzones
from .pattern_detector import detect_major_sweep_and_shift_patterns

logger = logging.getLogger(__name__)

def analyze_ict_concepts_v4(df_ohlcv: pd.DataFrame, current_time_utc_iso: str | None = None,
                            config_params: dict | None = None, timeframe_label: str = "Unknown_TF") -> dict:
    """
    تابع اصلی و به‌روز شده برای اجرای تمامی تحلیل‌های ICT (نسخه ۴).
    Orchestrates all ICT analysis components, ensuring correct parameter passing.
    """
    logger.info(f"====== شروع تحلیل جامع ICT (پکیج نسخه ۴, verified params v2) برای تایم فریم {timeframe_label} ======")
    if df_ohlcv.empty:
        logger.error("دیتافریم ورودی خالی است. تحلیل متوقف شد.")
        return {"error": "Input dataframe is empty.", "analysis_summary": "No data", "config_params_used": config_params}

    # --- 1. Parameter Configuration ---
    cfg = {}
    
    # Helper to populate cfg from constants, ensuring keys match detector param names
    def _populate_cfg_from_constants_module(target_cfg_dict, cfg_keys_list_name_in_constants):
        if hasattr(constants, cfg_keys_list_name_in_constants):
            keys_list_from_constants_module = getattr(constants, cfg_keys_list_name_in_constants)
            if isinstance(keys_list_from_constants_module, list):
                for key_param_name in keys_list_from_constants_module:
                    # Expected constant name format: PARAM_NAME_DEFAULT or just PARAM_NAME (if it's an explicit list like OTE_FIB_LEVELS)
                    const_name_cand1 = key_param_name.upper() + "_DEFAULT" 
                    const_name_cand2 = key_param_name.upper()
                    
                    if hasattr(constants, const_name_cand1):
                        target_cfg_dict[key_param_name] = getattr(constants, const_name_cand1)
                    elif hasattr(constants, const_name_cand2):
                        target_cfg_dict[key_param_name] = getattr(constants, const_name_cand2)
                    # else: logger.debug(f"Default for cfg key '{key_param_name}' from {cfg_keys_list_name_in_constants} not in constants.py as {const_name_cand1} or {const_name_cand2}.")
            else:
                logger.warning(f"'{cfg_keys_list_name_in_constants}' in constants.py is not a list.")
        else:
            logger.warning(f"CFG Keys list '{cfg_keys_list_name_in_constants}' not defined in constants.py.")

    # Populate cfg using all defined CFG_KEYS lists from constants.py
    all_cfg_key_lists_names = [
        "TREND_DETECTOR_CFG_KEYS", "SWING_DETECTOR_CFG_KEYS",
        "FVG_DETECTOR_CFG_KEYS", "STRUCTURE_DETECTOR_CFG_KEYS",
        "OB_DETECTOR_CFG_KEYS", "BB_DETECTOR_CFG_KEYS",
        "LIQ_DETECTOR_CFG_KEYS", "OTE_DETECTOR_CFG_KEYS",
        "SWEEP_SHIFT_DETECTOR_CFG_KEYS"
    ]
    for list_name in all_cfg_key_lists_names:
        _populate_cfg_from_constants_module(cfg, list_name)

    # Base parameters not in CFG_KEYS lists but needed directly, or specific overrides/aliases
    cfg["atr_period"] = constants.ATR_PERIOD_DEFAULT # Explicitly set, also in some CFG_KEYS but good to be sure
    cfg["final_killzone_definitions"] = list(constants.DEFAULT_KILLZONE_DEFINITIONS) # Use a copy
    
    # Ensure specific aliases or critical defaults are set correctly
    # These map constant names (often verbose or with _DEFAULT) to the concise parameter names used in functions / CFG_KEYS
    cfg["swing_window"] = constants.DEFAULT_SWING_WINDOW # For explicit_args_map for detect_swings
    cfg["window"] = constants.DEFAULT_SWING_WINDOW      # Ensure 'window' key is also in cfg for detect_swings if used directly by CFG_KEY list

    # Example: OB detector 'displacement_multiplier' parameter uses OB_DISPLACEMENT_ATR_MULTIPLIER_DEFAULT
    cfg["displacement_multiplier"] = constants.OB_DISPLACEMENT_ATR_MULTIPLIER_DEFAULT
    cfg["mitigation_lookahead"] = constants.OB_MITIGATION_LOOKAHEAD_CANDLES_DEFAULT # For OB
    cfg["unmitigated_factor"] = constants.OB_UNMITIGATED_STRENGTH_FACTOR_DEFAULT # For OB, and FVG uses FVG_UNMITIGATED_IMPORTANCE_FACTOR_DEFAULT
    
    # For BB detector, ensure mapping from constants to specific parameter names used in its CFG_KEYS list
    cfg["break_strength_factor_for_bb_score"] = constants.BB_BREAK_STRENGTH_FACTOR_DEFAULT
    cfg["displacement_through_ob_factor"] = constants.BB_DISPLACEMENT_THROUGH_OB_FACTOR_DEFAULT # For BB
    
    # For OTE, 'ote_fib_levels' in CFG_KEYS should pick up OTE_FIB_LEVELS_STANDARD_DEFAULT
    cfg["ote_fib_levels"] = list(constants.OTE_FIB_LEVELS_STANDARD_DEFAULT)


    if config_params: 
        for key, value in config_params.items():
            normalized_key = key.lower().replace('-', '_') 
            # Update if key exists, or add if it's a new potential override
            cfg[normalized_key] = value 
            if normalized_key != key and key in cfg: # If original case key existed, update it too for safety, though normalized should be preferred
                cfg[key] = value
            if normalized_key not in cfg and key not in cfg:
                 logger.debug(f"Custom config param '{key}' (as '{normalized_key}') added to cfg as new entry.")
    
    logger.info(f"Analyzer '{timeframe_label}' using effective config (subset shown): "
                f"atr_period={cfg.get('atr_period')}, swing_window={cfg.get('swing_window', cfg.get('window'))}, "
                f"short_ema_p={cfg.get('short_ema_p')}")


    # --- 2. Data Preparation ---
    df = df_ohlcv.copy(); 
    if not isinstance(df.index, pd.DatetimeIndex): df.index = pd.to_datetime(df.index); 
    df.index = df.index.tz_localize('UTC') if df.index.tzinfo is None else df.index.tz_convert('UTC'); 
    df_proc = df.rename(columns={'Open':'open', 'High':'high', 'Low':'low', 'Close':'close', 'Volume':'volume'}, errors='ignore'); 

    current_time_for_analysis_dt = datetime.now(timezone.utc); 
    if current_time_utc_iso: 
        try:
            dt_object = datetime.fromisoformat(current_time_utc_iso.replace("Z", "+00:00")); 
            current_time_for_analysis_dt = dt_object.astimezone(pytz.utc) if dt_object.tzinfo else pytz.utc.localize(dt_object); 
        except ValueError: logger.warning(f"Invalid ISO: {current_time_utc_iso}."); 
    elif not df_proc.empty: current_time_for_analysis_dt = df_proc.index[-1]; 

    all_ict_results = { "analysis_timestamp_utc": current_time_for_analysis_dt.isoformat(), "timeframe_analyzed": timeframe_label, "config_params_used": cfg.copy() }; 

    df_with_atr = ensure_atr(df_ohlcv.copy(), period=cfg["atr_period"]); 
    all_ict_results['atr_latest'] = round(df_with_atr['ATR_Value'].iloc[-1], 6) if not df_with_atr.empty and 'ATR_Value' in df_with_atr.columns and pd.notna(df_with_atr['ATR_Value'].iloc[-1]) else None; 
    if all_ict_results['atr_latest'] is None: logger.warning(f"ATR_latest not determined for {timeframe_label}.");

    high_col_name = 'High' if 'High' in df_with_atr.columns else 'high'
    low_col_name = 'Low' if 'Low' in df_with_atr.columns else 'low'
    close_col_name = 'Close' if 'Close' in df_with_atr.columns else 'close'
    open_col_name = 'Open' if 'Open' in df_with_atr.columns else 'open'

    # --- 3. Sequential Analysis ---
    def get_kwargs_for_detector(detector_cfg_keys_list_name: str, explicit_args_map: Optional[Dict[str,str]] = None) -> Dict[str, Any]:
        kwargs = {}
        # Handle explicitly mapped parameters first
        if explicit_args_map:
            for func_param_name, cfg_key_name in explicit_args_map.items():
                if cfg_key_name in cfg:
                    kwargs[func_param_name] = cfg[cfg_key_name]
        
        # Handle keys from the CFG_KEYS list
        if hasattr(constants, detector_cfg_keys_list_name):
            keys_list_from_const = getattr(constants, detector_cfg_keys_list_name)
            if isinstance(keys_list_from_const, list):
                for key_param_name in keys_list_from_const: # These keys should match function param names
                    # Only add if not already handled by explicit_args_map AND key is in cfg
                    if (explicit_args_map is None or key_param_name not in explicit_args_map.keys()) and key_param_name in cfg:
                        kwargs[key_param_name] = cfg[key_param_name]
            else: logger.warning(f"'{detector_cfg_keys_list_name}' in constants.py is not a list.")
        else: logger.warning(f"CFG Keys list '{detector_cfg_keys_list_name}' not defined in constants.py.")
        return kwargs

    trend_params = get_kwargs_for_detector("TREND_DETECTOR_CFG_KEYS")
    market_trend_details = determine_market_trend(df_proc, **trend_params)
    all_ict_results['market_trend_analysis'] = market_trend_details

    swing_params = get_kwargs_for_detector("SWING_DETECTOR_CFG_KEYS", explicit_args_map={"window": "swing_window", "atr_period": "atr_period"})
    raw_swings_list = detect_swings(df_with_atr, fvgs=None, **swing_params)

    fvg_params = get_kwargs_for_detector("FVG_DETECTOR_CFG_KEYS", explicit_args_map={"atr_period":"atr_period"})
    initial_fvg_list = find_fvgs(df_with_atr, swings=raw_swings_list, bos_choch_events=None, **fvg_params)

    structure_params = get_kwargs_for_detector("STRUCTURE_DETECTOR_CFG_KEYS")
    # Ensure essential direct args are passed if not in **structure_params or if needed for clarity
    structure_params.pop('atr_period', None) # Will be passed explicitly
    structure_params.pop('fvgs_list', None)  # Will be passed explicitly
    bos_choch_event_list, updated_swings_with_structure = find_bos_choch(
        df_with_atr, raw_swings_list, market_trend_details,
        atr_period=cfg["atr_period"], fvgs_list=initial_fvg_list, **structure_params
    )
    all_ict_results['bos_choch_events'] = bos_choch_event_list
    all_ict_results['swings_analysis'] = updated_swings_with_structure
    
    refined_fvg_list = find_fvgs(
        df_with_atr, swings=updated_swings_with_structure, bos_choch_events=bos_choch_event_list, **fvg_params
    )
    all_ict_results['fair_value_gaps'] = refined_fvg_list

    ob_params = get_kwargs_for_detector("OB_DETECTOR_CFG_KEYS")
    order_block_list = find_order_blocks( 
        df_with_atr, updated_swings_with_structure, refined_fvg_list, 
        bos_choch_event_list, atr_period=cfg["atr_period"], **ob_params 
    )
    all_ict_results['order_blocks'] = order_block_list

    bb_params = get_kwargs_for_detector("BB_DETECTOR_CFG_KEYS")
    breaker_block_list = find_breaker_blocks( 
        df_with_atr, order_block_list, bos_choch_event_list, 
        refined_fvg_list, atr_period=cfg["atr_period"], **bb_params 
    )
    all_ict_results['breaker_blocks'] = breaker_block_list
    
    latest_dr_details_for_liq = None
    if bos_choch_event_list:
        significant_dr_events = [e for e in bos_choch_event_list if isinstance(e, dict) and e.get('new_dealing_range_established') and isinstance(e.get('new_dealing_range_details'), dict) and e.get('confirmation_quality_score',0) >= 5.0 ]
        if significant_dr_events:
            latest_event = max(significant_dr_events, key=lambda x: pd.to_datetime(x.get('timestamp_break', pd.NaT), utc=True))
            if pd.notna(latest_event.get('timestamp_break')): latest_dr_details_for_liq = latest_event['new_dealing_range_details']
    
    # find_key_liquidity_pools takes cfg directly now
    key_liquidity_pool_list = find_key_liquidity_pools(
        df_with_atr, swings=updated_swings_with_structure, 
        current_time_utc_iso=current_time_for_analysis_dt.isoformat(),
        dealing_range_details=latest_dr_details_for_liq,
        atr_period=cfg["atr_period"], 
        pd_arrays_for_inducement={'fvgs': refined_fvg_list, 'order_blocks': order_block_list},
        bos_choch_events=bos_choch_event_list, cfg=cfg 
    )
    all_ict_results['key_liquidity_pools'] = key_liquidity_pool_list

    # Call Major Sweep & Shift Pattern Detection (takes cfg directly)
    major_sweep_shift_patterns_found = detect_major_sweep_and_shift_patterns(
        df_with_atr,
        all_ict_results.get('key_liquidity_pools', []),
        all_ict_results.get('bos_choch_events', []),
        cfg, 
        high_col_name, low_col_name, close_col_name, open_col_name
    )
    if major_sweep_shift_patterns_found:
        all_ict_results['all_major_sweep_shift_patterns'] = major_sweep_shift_patterns_found
        all_ict_results['major_sweep_shift_pattern_details'] = sorted(
            major_sweep_shift_patterns_found,
            key=lambda x: x.get('pattern_confidence_score', 0),
            reverse=True
        )[0] if major_sweep_shift_patterns_found else None
    else:
        all_ict_results['all_major_sweep_shift_patterns'] = []
        all_ict_results['major_sweep_shift_pattern_details'] = None

    market_killzone_periods = get_market_killzones(
        current_time_for_analysis_dt.isoformat(),
        killzone_definitions=cfg.get("final_killzone_definitions", []) 
    )
    all_ict_results['market_killzones_and_shoulders'] = market_killzone_periods

    # find_ote_levels takes cfg directly
    pd_arrays_for_ote_calc = { 'fvgs': refined_fvg_list, 'order_blocks': order_block_list, 'breaker_blocks': breaker_block_list }
    ote_level_list = find_ote_levels(
        df_with_atr, bos_choch_event_list, pd_arrays_for_ote_calc,
        key_liquidity_pool_list, 
        killzones_info=market_killzone_periods,
        atr_period=cfg["atr_period"], 
        cfg=cfg 
    )
    all_ict_results['optimal_trade_entry_zones'] = ote_level_list

    logger.info(f"====== تحلیل جامع ICT (پکیج نسخه ۴, verified params v2) برای تایم فریم {timeframe_label} با موفقیت انجام شد ======")
    return all_ict_results

# --- Example Usage (for testing within this file if run directly) ---
if __name__ == '__main__': 
    pass

logger.info("ict_analyzer_package/analyzer.py (fully updated and verified for param passing v2) loaded.")
# setup_tracker.py
"""
ماژول برای پیگیری وضعیت ستاپ‌های معاملاتی فعال (نسخه بهبود یافته با گزارش‌دهی پیشرفته و منطق دقیق‌تر)
Module for tracking the status of active trading setups (Enhanced Version with Advanced Reporting and refined logic).
"""
import json
import os
import logging
from datetime import datetime, timezone, timedelta
import time # Kept for potential future use, though current sleep is in track_and_report_all_active_setups
import pandas as pd
import asyncio # For track_and_report_all_active_setups sleep if made async
from typing import Tuple, Dict, Any, Optional, List # Added List

import config
from api_clients import fetch_ohlcv_cryptocompare
from telegram_sender import send_to_telegram

logger = logging.getLogger(__name__)

def _get_active_setups_filepath() -> str: #
    if not config.OUTPUT_DIR or config.OUTPUT_DIR == "output_dir_placeholder":
        logger.error("OUTPUT_DIR in config not properly set. Using current working directory for active_setups.json.")
        # Ensure current working directory is writable, or specify a known good fallback
        fallback_dir = os.path.join(os.getcwd(), "tracker_data_fallback")
        os.makedirs(fallback_dir, exist_ok=True)
        return os.path.join(fallback_dir, config.ACTIVE_SETUPS_FILENAME)
    return os.path.join(config.OUTPUT_DIR, config.ACTIVE_SETUPS_FILENAME)

def load_active_setups() -> List[Dict[str, Any]]: #
    filepath = _get_active_setups_filepath()
    try:
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f: setups = json.load(f)
            if not isinstance(setups, list):
                logger.warning(f"{filepath} does not contain a list. Returning empty list.")
                return []
            # Basic validation for essential keys in each setup dict (optional, but good practice)
            # for setup in setups:
            #     if not all(k in setup for k in ["setup_id", "status", "direction"]):
            #         logger.warning(f"Loaded setup missing essential keys: {setup.get('setup_id', 'Unknown ID')}")
            return setups
        else:
            logger.info(f"Active setups file ({filepath}) not found. Returning empty list.")
            return []
    except json.JSONDecodeError:
        logger.error(f"Error decoding JSON from {filepath}. Returning empty list.")
        return []
    except Exception as e:
        logger.error(f"Unexpected error loading {filepath}: {e}", exc_info=True)
        return []

def save_active_setups(active_setups_list: List[Dict[str, Any]]): #
    filepath = _get_active_setups_filepath()
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(active_setups_list, f, ensure_ascii=False, indent=2)
        logger.info(f"Active setups list saved to {filepath} with {len(active_setups_list)} setups.")
    except Exception as e:
        logger.error(f"Error saving active setups to {filepath}: {e}", exc_info=True)

def add_new_setup(setup_details: Dict[str, Any]): #
    if not isinstance(setup_details, dict):
        logger.error("Invalid setup_details provided to add_new_setup (not a dict).")
        return
    active_setups = load_active_setups()
    if any(s.get("setup_id") == setup_details.get("setup_id") for s in active_setups):
        logger.warning(f"Setup ID '{setup_details.get('setup_id')}' already exists. Not adding.")
        return
    
    # Ensure essential fields for tracking are present or defaulted
    setup_details.setdefault("status", "pending_entry")
    setup_details.setdefault("creation_timestamp_utc", datetime.now(timezone.utc).isoformat())
    setup_details.setdefault("last_checked_timestamp_utc", datetime.now(timezone.utc).isoformat())
    setup_details.setdefault("notes_tracker", [f"Setup added at {setup_details['creation_timestamp_utc']}"])
    setup_details.setdefault("current_tp_level_reached", 0)
    
    active_setups.append(setup_details)
    save_active_setups(active_setups)
    logger.info(f"New setup ID '{setup_details.get('setup_id')}' added. Status: {setup_details['status']}, Expiry: {setup_details.get('expiry_timestamp_utc')}")


def get_open_setup_for_symbol(fsym: str, tsym: str) -> Optional[Dict[str, Any]]: #
    active_setups = load_active_setups()
    for setup in active_setups:
        if setup.get("symbol_fsym") == fsym and setup.get("symbol_tsym") == tsym:
            status = setup.get("status", "").lower()
            if "sl_hit" not in status and \
               "all_tps_hit" not in status and \
               "manually_closed" not in status and \
               not status.startswith("expired") and \
               not status.startswith("invalidated"):
                return setup
    return None

def check_and_update_single_setup(setup: Dict[str, Any], ohlcv_data: pd.DataFrame) -> Tuple[Dict[str, Any], bool]: #
    """
    (Rewritten for Robust Entry/SL/TP Logic and Expiry - from response #47, refined)
    Checks and updates the status of a single setup. SL/TP checks begin on candles AFTER entry.
    """
    if not isinstance(setup, dict) or ohlcv_data.empty:
        logger.warning("Invalid setup or empty OHLCV for check_and_update_single_setup.")
        return setup, False

    status_changed = False
    original_status = str(setup.get("status", "pending_entry")) # Ensure string for .lower()
    notes = setup.get("notes_tracker", [])
    if not isinstance(notes, list): notes = [str(notes) if notes else "Init note missing"]

    # Standardize OHLCV data index and column names
    if not isinstance(ohlcv_data.index, pd.DatetimeIndex):
        logger.error("OHLCV data index is not DatetimeIndex. Aborting setup check."); return setup, False
    ohlcv_data.index = pd.to_datetime(ohlcv_data.index, utc=True).tz_convert('UTC')
    if not ohlcv_data.index.is_monotonic_increasing: ohlcv_data.sort_index(inplace=True)
    
    col_map = {
        'high': 'High' if 'High' in ohlcv_data.columns else 'high',
        'low': 'Low' if 'Low' in ohlcv_data.columns else 'low',
        'close': 'Close' if 'Close' in ohlcv_data.columns else 'close' # Needed for future logic potentially
    }
    if not all(c in ohlcv_data.columns for c in col_map.values()):
        logger.error(f"OHLCV data missing one or more required columns: {list(col_map.values())}"); return setup, False


    # --- 1. Handle Pending Setups: Expiry and Entry Activation ---
    if original_status == "pending_entry":
        # A. Check for Time-Based Expiry first
        expiry_ts_str = setup.get("expiry_timestamp_utc")
        if expiry_ts_str:
            expiry_ts = pd.to_datetime(expiry_ts_str, utc=True)
            current_data_latest_ts = ohlcv_data.index[-1].to_pydatetime().replace(tzinfo=timezone.utc)
            
            if current_data_latest_ts > expiry_ts: # If latest data point is past expiry
                setup["status"] = "expired_pending_time"
                notes.append(f"Setup expired at {expiry_ts.strftime('%Y-%m-%d %H:%M UTC')} before entry (last data at {current_data_latest_ts.strftime('%Y-%m-%d %H:%M UTC')}).")
                logger.info(f"Setup ID {setup.get('setup_id')} expired by time before entry.")
                status_changed = True
                setup["last_checked_timestamp_utc"] = datetime.now(timezone.utc).isoformat()
                setup["notes_tracker"] = notes
                return setup, status_changed

        # B. Check for Entry Activation
        start_iter_index = 0
        creation_ts_str = setup.get("creation_timestamp_utc")
        if creation_ts_str:
            creation_ts = pd.to_datetime(creation_ts_str, utc=True)
            relevant_indices = ohlcv_data.index[ohlcv_data.index >= creation_ts]
            if not relevant_indices.empty:
                try: start_iter_index = ohlcv_data.index.get_loc(relevant_indices[0])
                except KeyError: pass # Fallback to processing_start_index = 0
            else: # No candles at or after creation time in current historical data
                notes.append(f"No new candles since creation for entry check (data up to {ohlcv_data.index[-1].strftime('%Y-%m-%d %H:%M UTC') if not ohlcv_data.empty else 'N/A'}).")
                setup["last_checked_timestamp_utc"] = datetime.now(timezone.utc).isoformat()
                setup["notes_tracker"] = notes
                return setup, False # No change, no relevant data to find entry

        for i in range(start_iter_index, len(ohlcv_data)):
            candle_data = ohlcv_data.iloc[i]
            candle_time = ohlcv_data.index[i].to_pydatetime().replace(tzinfo=timezone.utc)
            
            entry_min = setup.get("entry_price_target_min"); entry_max = setup.get("entry_price_target_max")
            direction = setup.get("direction", "").lower()
            entered_this_candle = False

            if direction == "buy" and candle_data[col_map['low']] <= entry_max and candle_data[col_map['high']] >= entry_min:
                entered_this_candle = True
            elif direction == "sell" and candle_data[col_map['high']] >= entry_min and candle_data[col_map['low']] <= entry_max:
                entered_this_candle = True
            
            if entered_this_candle:
                setup["status"] = "active"
                setup["activation_timestamp_utc"] = candle_time.isoformat()
                entry_confirm_price = entry_min if direction == "sell" else entry_max 
                notes.append(f"Entry activated on candle {candle_time.strftime('%Y-%m-%d %H:%M')} (price range {entry_min}-{entry_max} touched). Assumed entry near {entry_confirm_price}.")
                status_changed = True
                original_status = "active" # Update current status for the next phase in this same function call
                logger.info(f"Setup ID {setup.get('setup_id')} activated at {candle_time.isoformat()}. SL/TP check will start from next candle if any.")
                break # Entry found, stop searching for more entries in this pass

        if not status_changed and original_status == "pending_entry": # Still pending after loop
             if not ohlcv_data.iloc[start_iter_index:].empty:
                notes.append(f"Entry conditions not met up to {ohlcv_data.index[-1].strftime('%Y-%m-%d %H:%M UTC')}.")
             else: # No relevant data was processed
                notes.append(f"No relevant data for entry check (from creation time or start_iter_index {start_iter_index}).")


    # --- 2. Handle Active Setups (or just activated): SL/TP Management ---
    current_status_for_sltp = setup.get("status", "").lower() # Get potentially updated status
    if current_status_for_sltp == "active" or "tp" in current_status_for_sltp:
        activation_ts_str = setup.get("activation_timestamp_utc")
        if not activation_ts_str: 
            logger.error(f"Setup {setup.get('setup_id')} is '{current_status_for_sltp}' but has no activation_timestamp_utc. Skipping SL/TP check.")
        else:
            activation_ts = pd.to_datetime(activation_ts_str, utc=True)
            
            # Find first candle strictly AFTER activation timestamp
            try:
                activation_candle_loc = ohlcv_data.index.get_loc(activation_ts)
                sltp_check_start_idx = activation_candle_loc + 1
            except KeyError: # Activation TS not in current ohlcv_data (e.g., from a previous run not overlapping)
                # If activation_ts is before the start of current ohlcv_data, process all current data
                if not ohlcv_data.empty and activation_ts < ohlcv_data.index[0]:
                    sltp_check_start_idx = 0
                else: # activation_ts is after or not found, no candles to check after activation in this batch
                    sltp_check_start_idx = len(ohlcv_data)
                    if current_status_for_sltp == "active": # Only note if it was expected to be active
                        notes.append(f"No new candles since activation at {activation_ts.strftime('%Y-%m-%d %H:%M UTC')} for SL/TP check (data up to {ohlcv_data.index[-1].strftime('%Y-%m-%d %H:%M UTC') if not ohlcv_data.empty else 'N/A'}).")

            if sltp_check_start_idx < len(ohlcv_data):
                for i in range(sltp_check_start_idx, len(ohlcv_data)):
                    candle_data = ohlcv_data.iloc[i]
                    candle_time = ohlcv_data.index[i].to_pydatetime().replace(tzinfo=timezone.utc)
                    candle_high = candle_data[col_map['high']]; candle_low = candle_data[col_map['low']]
                    
                    sl_price = setup.get("stop_loss_price"); direction = setup.get("direction","").lower()
                    sl_hit_this_candle = False
                    if direction == "buy" and candle_low <= sl_price: sl_hit_this_candle = True
                    elif direction == "sell" and candle_high >= sl_price: sl_hit_this_candle = True

                    if sl_hit_this_candle:
                        setup["status"] = "sl_hit"; setup["closed_timestamp_utc"] = candle_time.isoformat(); setup["closing_price"] = sl_price
                        notes.append(f"Stop Loss hit at {sl_price} on candle {candle_time.strftime('%Y-%m-%d %H:%M')}.")
                        status_changed = True; logger.info(f"Setup ID {setup.get('setup_id')} SL hit."); break

                    tp_prices = setup.get("take_profit_prices", [])
                    current_tp_level = setup.get("current_tp_level_reached", 0)
                    
                    for tp_idx in range(current_tp_level, len(tp_prices)):
                        target_tp_price = tp_prices[tp_idx]; tp_hit_this_candle = False
                        if direction == "buy" and candle_high >= target_tp_price: tp_hit_this_candle = True
                        elif direction == "sell" and candle_low <= target_tp_price: tp_hit_this_candle = True
                        
                        if tp_hit_this_candle:
                            current_tp_level = tp_idx + 1; setup["current_tp_level_reached"] = current_tp_level
                            new_tp_status = f"tp{current_tp_level}_hit"
                            notes.append(f"TP{current_tp_level} hit at {target_tp_price} on candle {candle_time.strftime('%Y-%m-%d %H:%M')}.")
                            status_changed = True; logger.info(f"Setup ID {setup.get('setup_id')} TP{current_tp_level} hit.")
                            if current_tp_level == len(tp_prices):
                                setup["status"] = "all_tps_hit"; setup["closed_timestamp_utc"] = candle_time.isoformat(); setup["closing_price"] = target_tp_price
                                notes.append("All TPs hit. Setup closed."); logger.info(f"Setup ID {setup.get('setup_id')} all TPs hit."); break
                            else: setup["status"] = new_tp_status
                        else: break 
                    if setup.get("status") == "all_tps_hit": break

    setup["last_checked_timestamp_utc"] = datetime.now(timezone.utc).isoformat()
    # Add a general note if no status change occurred but candles were processed
    if not status_changed and original_status == setup.get("status"):
        if not ohlcv_data.empty and processing_start_index < len(ohlcv_data) : # If some data was iterable
            last_processed_candle_ts_str = ohlcv_data.index[-1].strftime('%Y-%m-%d %H:%M UTC')
            current_status_note = setup.get("status", "pending_entry")
            if current_status_note == "pending_entry" and setup.get("activation_timestamp_utc") is None:
                 # This means it was pending and still pending after checks
                pass # Already noted if entry conditions not met
            elif current_status_note != "pending_entry" and sltp_check_start_idx >= len(ohlcv_data) :
                # This means it was active, but no new candles for SL/TP check were available in this ohlcv_data batch
                pass # Already noted above
            elif current_status_note != "pending_entry":
                notes.append(f"Checked active setup up to {last_processed_candle_ts_str}, no SL/further TP hit from '{original_status}'.")

    setup["notes_tracker"] = notes
    return setup, status_changed

def format_timedelta(td_object: Optional[timedelta]) -> str: #
    if td_object is None: return "N/A"
    days = td_object.days; hours, remainder = divmod(td_object.seconds, 3600); minutes, _ = divmod(remainder, 60)
    parts = []
    if days > 0: parts.append(f"{days}d")
    if hours > 0: parts.append(f"{hours}h")
    if minutes > 0 or (days == 0 and hours == 0): parts.append(f"{minutes}m")
    return " ".join(parts) if parts else "~0m" # Show ~0m instead of blank

def format_setup_status_report(setup: Dict[str, Any]) -> str: #
    if not isinstance(setup, dict): return "Error: Invalid setup details for report."
    report_lines = []; now_utc = datetime.now(timezone.utc)
    status_emojis = {"pending_entry":"⏳", "active":"✅", "sl_hit":"🛑", "all_tps_hit":"🎉🏆", "expired_pending_time":"⌛️💨", "invalidated_pending_price":"⚠️❌", "manually_closed":" manually_closed "}
    for i in range(1, 10): status_emojis[f"tp{i}_hit"] = f"🎯 TP{i}"
    dir_emoji_map = {"buy": "📈", "sell": "📉"}

    def format_dt(dt_str: Optional[str]) -> str:
        if not dt_str: return "`N/A`"
        try: return f"`{pd.to_datetime(dt_str, utc=True).strftime('%Y-%m-%d %H:%M')}` UTC"
        except: return f"`{dt_str}` (unparsed)"

    setup_id = setup.get('setup_id', 'N/A'); status_raw = setup.get('status', 'N/A').lower()
    status_emoji = status_emojis.get(status_raw, "ℹ️"); status_text = status_raw.replace('_', ' ').capitalize()
    report_lines.append(f"{status_emoji} *═══ 📋 SETUP STATUS ═══* {status_emoji}")
    report_lines.append(f"*ID*: `{setup_id}`"); report_lines.append(f"*Symbol*: *{setup.get('ticker_display_name', 'N/A')}*")
    direction_val = setup.get('direction', '').lower(); dir_emoji = dir_emoji_map.get(direction_val, "")
    report_lines.append(f"*Direction*: {dir_emoji} *{direction_val.capitalize() if direction_val else 'N/A'}*")
    report_lines.append(f"*Status*: *{status_text}*")
    if setup.get('setup_timeframe'): report_lines.append(f"*Setup TF*: `{setup.get('setup_timeframe')}`")
    if setup.get('ai_confidence'): report_lines.append(f"*AI Confidence*: `{setup.get('ai_confidence')}` (Reason: _{setup.get('ai_confidence_reason', 'N/A')}_)")
    report_lines.append("")
    report_lines.append(f"*═══ ⏱️ TIMING ═══*"); report_lines.append(f"*Created*: {format_dt(setup.get('creation_timestamp_utc'))}")
    if status_raw == "pending_entry":
        if expiry_ts_str := setup.get('expiry_timestamp_utc'):
            expiry_dt = pd.to_datetime(expiry_ts_str, utc=True)
            report_lines.append(f"*Expires*: {format_dt(expiry_ts_str)}")
            if now_utc < expiry_dt: report_lines.append(f"*Time Left*: `{format_timedelta(expiry_dt - now_utc)}`")
            else: report_lines.append(f"*Time Left*: `EXPIRED` (missed)") # Clarify if missed due to time
        if not setup.get('activation_timestamp_utc'): report_lines.append(f"*Activated*: `Pending Activation`")
    elif act_ts_str := setup.get('activation_timestamp_utc'):
        activated_dt = pd.to_datetime(act_ts_str, utc=True)
        report_lines.append(f"*Activated*: {format_dt(act_ts_str)}")
        if "sl_hit" not in status_raw and "all_tps_hit" not in status_raw and not status_raw.startswith("expired") and not status_raw.startswith("invalidated"):
            report_lines.append(f"*Duration Active*: `{format_timedelta(now_utc - activated_dt)}`")
    is_closed_status = "sl_hit" in status_raw or "all_tps_hit" in status_raw or status_raw.startswith("expired") or status_raw.startswith("invalidated") or "manually_closed" in status_raw
    if is_closed_status and setup.get('closed_timestamp_utc') and (status_raw not in ["pending_entry", "active"] and not status_raw.startswith("tp") or status_raw == "all_tps_hit"): # Show for all closed types
        report_lines.append(f"*Closed*: {format_dt(setup.get('closed_timestamp_utc'))}")
    report_lines.append(f"*Last Checked*: {format_dt(setup.get('last_checked_timestamp_utc'))}"); report_lines.append("")
    report_lines.append(f"*═══ 📈 TRADE PARAMETERS ═══*")
    report_lines.append(f"*Entry Zone*: `{setup.get('entry_price_target_min', 'N/A')}` - `{setup.get('entry_price_target_max', 'N/A')}`")
    report_lines.append(f"*Stop Loss*: `{setup.get('stop_loss_price', 'N/A')}` 🛑")
    tps = setup.get('take_profit_prices', []); tps_reached_count = setup.get('current_tp_level_reached', 0)
    if tps:
        report_lines.append(f"*Take Profits* ({tps_reached_count}/{len(tps)} hit):")
        for i, tp_price in enumerate(tps):
            tp_status_emoji = "✅" if (i + 1) <= tps_reached_count else "➖"
            report_lines.append(f"  - TP{i+1}: `{tp_price}` {tp_status_emoji}")
    report_lines.append("")
    if is_closed_status:
        report_lines.append(f"*═══ 🏁 OUTCOME ═══*"); outcome_emoji = status_emojis.get(status_raw, "ℹ️")
        report_lines.append(f"*Result*: {outcome_emoji} *{status_text}*")
        if setup.get('closing_price'): report_lines.append(f"*Closing Price*: `{setup.get('closing_price')}`")
        if setup.get('closed_timestamp_utc') and (status_raw.startswith("tp") or status_raw == "sl_hit") and setup.get('activation_timestamp_utc'): # Show only if it was active before closing
             if not (status_raw not in ["pending_entry", "active"] and not status_raw.startswith("tp") or status_raw == "all_tps_hit"): # Avoid re-printing
                report_lines.append(f"*Closed At*: {format_dt(setup.get('closed_timestamp_utc'))}")
        report_lines.append("")
    notes = setup.get('notes_tracker', [])
    report_lines.append(f"*═══ 📝 RECENT NOTES ({len(notes)} total) ═══*")
    if notes and isinstance(notes, list):
        for note in notes[-3:]: report_lines.append(f"- _{note}_")
    else: report_lines.append("- _No recent notes._")
    report_lines.append("")
    entry_suggestion = setup.get('entry_suggestion_text', 'N/A')
    if entry_suggestion and entry_suggestion != 'N/A':
        report_lines.append(f"*═══ 💡 AI SUGGESTION ═══*")
        max_suggestion_len = 200 
        display_suggestion = (entry_suggestion[:max_suggestion_len] + '...' if len(entry_suggestion) > max_suggestion_len else entry_suggestion)
        report_lines.append(f"_{display_suggestion}_")
    return "\n".join(report_lines)

async def track_and_report_all_active_setups(send_to_telegram_flag: bool = False) -> tuple[list, str]: #
    logger.info("Starting check and report for all active setups...") 
    active_setups = load_active_setups() 
    updated_setups_list = []
    all_reports_text_parts = ["--- Comprehensive Active Setups Status Report ---"]
    any_status_changed_globally = False
    if not active_setups: 
        no_setups_msg = "No active setups currently being tracked." 
        logger.info(no_setups_msg); all_reports_text_parts.append(no_setups_msg) 
        return [], "\n".join(all_reports_text_parts)
    for setup in active_setups: 
        setup_id = setup.get("setup_id", "Unknown_ID"); current_status = setup.get("status", "").lower() 
        if "sl_hit" in current_status or "all_tps_hit" in current_status or "manually_closed" in current_status or current_status.startswith("expired") or current_status.startswith("invalidated"): 
            updated_setups_list.append(setup); report = format_setup_status_report(setup) 
            all_reports_text_parts.append(f"\n{'='*30}\n{report}\n{'='*30}"); continue 
        fsym, tsym = setup.get("symbol_fsym"), setup.get("symbol_tsym") 
        if not fsym or not tsym: logger.warning(f"Invalid symbol for setup ID: {setup_id}. Skipping."); updated_setups_list.append(setup); continue
        logger.info(f"Fetching price data for {fsym}-{tsym} (Setup ID: {setup_id})...") 
        ohlcv_data_for_tracking = fetch_ohlcv_cryptocompare(fsym=fsym, tsym=tsym, endpoint_type='minute', aggregate_minutes=config.SETUP_TRACKER_TIMEFRAME_MINUTES, history_days=config.SETUP_TRACKER_HISTORY_DAYS, limit_per_api_call=2000, api_key=config.CRYPTOCOMPARE_API_KEY)
        updated_setup_details, status_changed_this_setup = setup.copy(), False 
        if ohlcv_data_for_tracking is not None and not ohlcv_data_for_tracking.empty: 
            updated_setup_details, status_changed_this_setup = check_and_update_single_setup(setup.copy(), ohlcv_data_for_tracking) 
        else: 
            logger.warning(f"No price data for {fsym}-{tsym} (Setup ID: {setup_id}). Status not updated.")
            notes_list = updated_setup_details.get("notes_tracker", [])
            if not isinstance(notes_list, list): notes_list = [str(notes_list) if notes_list else ""]
            notes_list.append(f"Failed to fetch price data at {datetime.now(timezone.utc).isoformat()} for check.")
            updated_setup_details["notes_tracker"] = notes_list; updated_setup_details["last_checked_timestamp_utc"] = datetime.now(timezone.utc).isoformat()
        updated_setups_list.append(updated_setup_details); report = format_setup_status_report(updated_setup_details) 
        all_reports_text_parts.append(f"\n{'='*30}\n{report}\n{'='*30}") 
        if status_changed_this_setup: any_status_changed_globally = True 
        await asyncio.sleep(0.5) 
    if any_status_changed_globally or True: save_active_setups(updated_setups_list) 
    final_report_string = "\n".join(all_reports_text_parts); print(final_report_string) 
    if send_to_telegram_flag and config.TELEGRAM_BOT_TOKEN and config.TELEGRAM_CHAT_ID: 
        logger.info("Sending comprehensive setup status report to Telegram...") 
        await send_to_telegram(text=final_report_string, bot_token=config.TELEGRAM_BOT_TOKEN, chat_id=config.TELEGRAM_CHAT_ID) 
    elif send_to_telegram_flag: logger.warning("Telegram Bot Token or Chat ID not set. Cannot send report.") 
    return updated_setups_list, final_report_string

async def track_specific_open_setup(fsym: str, tsym: str, send_to_telegram_flag: bool = False) -> tuple[Optional[dict], str]: #
    # ... (Full implementation from response #47, which calls the enhanced check_and_update_single_setup)
    logger.info(f"Starting specific tracking for open setup: {fsym}-{tsym}...") 
    open_setup = get_open_setup_for_symbol(fsym, tsym) 
    if not open_setup: 
        report_str = f"No active open setup found for {fsym}-{tsym}." 
        logger.info(report_str) 
        if send_to_telegram_flag and config.TELEGRAM_BOT_TOKEN and config.TELEGRAM_CHAT_ID: await send_to_telegram(text=report_str, bot_token=config.TELEGRAM_BOT_TOKEN, chat_id=config.TELEGRAM_CHAT_ID) 
        print(report_str); return None, report_str
    setup_id = open_setup.get("setup_id", "Unknown_ID") 
    logger.info(f"Fetching price data for {fsym}-{tsym} (Setup ID: {setup_id}) for specific tracking...") 
    ohlcv_data_tracking = fetch_ohlcv_cryptocompare(fsym=fsym, tsym=tsym, endpoint_type='minute', aggregate_minutes=config.SETUP_TRACKER_TIMEFRAME_MINUTES, history_days=config.SETUP_TRACKER_HISTORY_DAYS, limit_per_api_call=2000, api_key=config.CRYPTOCOMPARE_API_KEY )
    updated_setup_details, status_changed = open_setup.copy(), False 
    if ohlcv_data_tracking is not None and not ohlcv_data_tracking.empty: 
        updated_setup_details, status_changed = check_and_update_single_setup(open_setup.copy(), ohlcv_data_tracking) 
    else: 
        logger.warning(f"No price data for {fsym}-{tsym} (Setup ID: {setup_id}). Status not updated for specific track.")
        notes_list = updated_setup_details.get("notes_tracker", [])
        if not isinstance(notes_list, list): notes_list = [str(notes_list) if notes_list else ""]
        notes_list.append(f"Specific track: Failed to fetch price data at {datetime.now(timezone.utc).isoformat()}.")
        updated_setup_details["notes_tracker"] = notes_list; updated_setup_details["last_checked_timestamp_utc"] = datetime.now(timezone.utc).isoformat()
    if status_changed: 
        active_setups_list = load_active_setups() 
        for i, s_item in enumerate(active_setups_list): 
            if s_item.get("setup_id") == setup_id: active_setups_list[i] = updated_setup_details; break
        save_active_setups(active_setups_list) 
    final_report_for_symbol = format_setup_status_report(updated_setup_details) 
    print(f"\n{'='*30}\n{final_report_for_symbol}\n{'='*30}") 
    if send_to_telegram_flag and config.TELEGRAM_BOT_TOKEN and config.TELEGRAM_CHAT_ID: 
        logger.info(f"Sending status report for {fsym}-{tsym} (Setup ID: {setup_id}) to Telegram...") 
        await send_to_telegram(text=final_report_for_symbol, bot_token=config.TELEGRAM_BOT_TOKEN, chat_id=config.TELEGRAM_CHAT_ID) 
    elif send_to_telegram_flag: logger.warning("Telegram Bot Token or Chat ID not set for specific track report.") 
    return updated_setup_details, final_report_for_symbol

logger.info("setup_tracker.py (enhanced with expiry, refined entry logic, and attractive reporting) loaded.")